<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_72" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Flutter View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="FlutterViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="y3c-jy-aDJ"/>
                        <viewControllerLayoutGuide type="bottom" id="wfy-db-euE"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="430" height="932"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="34" y="31"/>
        </scene>
        <!--Onboarding1-->
        <scene sceneID="wbb-bn-8xH">
            <objects>
                <viewController storyboardIdentifier="page1" title="Onboarding1" id="Sep-nF-CnR" userLabel="Onboarding1" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="uNZ-m8-z9R">
                        <rect key="frame" x="0.0" y="0.0" width="430" height="932"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="LoveSync" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6cV-yO-fTW">
                                <rect key="frame" x="30" y="60" width="79" height="21"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="21" id="9Dm-a3-ls5"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hpO-dt-lJv">
                                <rect key="frame" x="344" y="54" width="36" height="33"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="33" id="dtU-8b-eNV"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                <state key="normal" title="Skip">
                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                            </button>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" image="firstPageImage" translatesAutoresizingMaskIntoConstraints="NO" id="c3w-mO-MP2">
                                <rect key="frame" x="30" y="106" width="331" height="432"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="Communicate with your partner sexually in a new &amp; different way." textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="GHw-5O-Cs6">
                                <rect key="frame" x="30" y="806" width="350" height="62"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="62" id="zl1-eu-96c"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="Strengthen intimacy &amp; connection" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XEX-bT-tUP">
                                <rect key="frame" x="30" y="573" width="370" height="225"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="225" id="2TY-8q-CBd"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="DINCondensed-Bold" family="DIN Condensed" pointSize="70"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="uu6-Gh-lLu"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="6cV-yO-fTW" firstAttribute="leading" secondItem="uu6-Gh-lLu" secondAttribute="leading" constant="30" id="3Qr-xI-FdE"/>
                            <constraint firstItem="c3w-mO-MP2" firstAttribute="top" secondItem="6cV-yO-fTW" secondAttribute="bottom" constant="25" id="3kC-HP-HGs"/>
                            <constraint firstItem="XEX-bT-tUP" firstAttribute="top" secondItem="c3w-mO-MP2" secondAttribute="bottom" constant="35" id="8At-IJ-6cs"/>
                            <constraint firstItem="uu6-Gh-lLu" firstAttribute="trailing" secondItem="c3w-mO-MP2" secondAttribute="trailing" constant="69" id="8KJ-ba-3Nw"/>
                            <constraint firstItem="XEX-bT-tUP" firstAttribute="leading" secondItem="GHw-5O-Cs6" secondAttribute="leading" id="Eot-J9-2iE"/>
                            <constraint firstItem="uu6-Gh-lLu" firstAttribute="bottom" secondItem="GHw-5O-Cs6" secondAttribute="bottom" constant="30" id="QTf-c3-8L0"/>
                            <constraint firstItem="GHw-5O-Cs6" firstAttribute="top" secondItem="XEX-bT-tUP" secondAttribute="bottom" constant="8" id="Qk4-j4-RUI"/>
                            <constraint firstItem="uu6-Gh-lLu" firstAttribute="trailing" secondItem="GHw-5O-Cs6" secondAttribute="trailing" constant="50" id="S2v-DF-UXo"/>
                            <constraint firstItem="c3w-mO-MP2" firstAttribute="leading" secondItem="uu6-Gh-lLu" secondAttribute="leading" constant="30" id="Vzk-vR-9WN"/>
                            <constraint firstItem="hpO-dt-lJv" firstAttribute="centerY" secondItem="6cV-yO-fTW" secondAttribute="centerY" id="a8b-lk-uPO"/>
                            <constraint firstItem="XEX-bT-tUP" firstAttribute="trailing" secondItem="uu6-Gh-lLu" secondAttribute="trailing" constant="-30" id="gBQ-zQ-3vX"/>
                            <constraint firstItem="6cV-yO-fTW" firstAttribute="top" secondItem="uNZ-m8-z9R" secondAttribute="top" constant="60" id="jOJ-BW-qHL"/>
                            <constraint firstItem="uu6-Gh-lLu" firstAttribute="trailing" secondItem="hpO-dt-lJv" secondAttribute="trailing" constant="50" id="oDO-0w-9Sf"/>
                            <constraint firstItem="XEX-bT-tUP" firstAttribute="leading" secondItem="uu6-Gh-lLu" secondAttribute="leading" constant="30" id="t2I-yf-YyF"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Vlp-oO-2FH" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-7.2463768115942031" y="-1249.5535714285713"/>
        </scene>
        <!--Onboarding2-->
        <scene sceneID="evV-h1-Xce">
            <objects>
                <viewController storyboardIdentifier="page2" title="Onboarding2" id="H88-jo-eSJ" userLabel="Onboarding2" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="b52-P0-V3N">
                        <rect key="frame" x="0.0" y="0.0" width="430" height="932"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="f1i-65-2vJ" customClass="GradientView" customModule="Runner" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="430" height="932"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                        <color key="value" red="1" green="0.60111642279999999" blue="0.39108792549999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                        <color key="value" red="1" green="0.50632911390000002" blue="0.50632911390000002" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isHorizontal" value="NO"/>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="LoveSync" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lff-B7-2kH">
                                <rect key="frame" x="30" y="60" width="79" height="21"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="SbE-x3-zJG">
                                <rect key="frame" x="344" y="54" width="36" height="33"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                <state key="normal" title="Skip">
                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                            </button>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" image="secondPageImage" translatesAutoresizingMaskIntoConstraints="NO" id="5e9-L7-7YT">
                                <rect key="frame" x="30" y="106" width="331" height="507"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="Express all your sexual desires because your partner will only know how you’re feeling if they are feeling the same." textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="d79-1w-vIE">
                                <rect key="frame" x="30" y="806" width="350" height="62"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="62" id="xlu-db-k8s"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="Don’t hold back" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XMr-8y-RAb">
                                <rect key="frame" x="30" y="648" width="328" height="150"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="BeN-oi-UNu"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="DINCondensed-Bold" family="DIN Condensed" pointSize="70"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="bCp-Oq-8UW"/>
                        <color key="backgroundColor" red="1" green="0.60111642279999999" blue="0.39108792549999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                        <constraints>
                            <constraint firstItem="lff-B7-2kH" firstAttribute="top" secondItem="b52-P0-V3N" secondAttribute="top" constant="60" id="2S9-bb-1s0"/>
                            <constraint firstItem="bCp-Oq-8UW" firstAttribute="trailing" secondItem="XMr-8y-RAb" secondAttribute="trailing" constant="72" id="3Cz-ts-vcL"/>
                            <constraint firstItem="XMr-8y-RAb" firstAttribute="top" secondItem="5e9-L7-7YT" secondAttribute="bottom" constant="35" id="CBR-mH-DuQ"/>
                            <constraint firstAttribute="bottom" secondItem="f1i-65-2vJ" secondAttribute="bottom" id="Cjq-51-znT"/>
                            <constraint firstItem="bCp-Oq-8UW" firstAttribute="trailing" secondItem="SbE-x3-zJG" secondAttribute="trailing" constant="50" id="O1Y-BC-IJC"/>
                            <constraint firstItem="5e9-L7-7YT" firstAttribute="leading" secondItem="lff-B7-2kH" secondAttribute="leading" id="OyV-na-3ug"/>
                            <constraint firstItem="XMr-8y-RAb" firstAttribute="leading" secondItem="d79-1w-vIE" secondAttribute="leading" id="PaW-jH-YYo"/>
                            <constraint firstItem="SbE-x3-zJG" firstAttribute="centerY" secondItem="lff-B7-2kH" secondAttribute="centerY" id="UWV-2L-Cue"/>
                            <constraint firstItem="d79-1w-vIE" firstAttribute="leading" secondItem="lff-B7-2kH" secondAttribute="leading" id="Ufg-8j-tGr"/>
                            <constraint firstItem="d79-1w-vIE" firstAttribute="top" secondItem="XMr-8y-RAb" secondAttribute="bottom" constant="8" id="Yev-XP-Tck"/>
                            <constraint firstItem="bCp-Oq-8UW" firstAttribute="bottom" secondItem="d79-1w-vIE" secondAttribute="bottom" constant="30" id="Yrz-kV-Ob5"/>
                            <constraint firstItem="f1i-65-2vJ" firstAttribute="top" secondItem="b52-P0-V3N" secondAttribute="top" id="dHU-ht-LVh"/>
                            <constraint firstItem="bCp-Oq-8UW" firstAttribute="trailing" secondItem="f1i-65-2vJ" secondAttribute="trailing" id="g04-0H-w98"/>
                            <constraint firstItem="f1i-65-2vJ" firstAttribute="leading" secondItem="bCp-Oq-8UW" secondAttribute="leading" id="jNp-5e-Pq2"/>
                            <constraint firstItem="bCp-Oq-8UW" firstAttribute="trailing" secondItem="d79-1w-vIE" secondAttribute="trailing" constant="50" id="ns0-To-Fg7"/>
                            <constraint firstItem="5e9-L7-7YT" firstAttribute="top" secondItem="lff-B7-2kH" secondAttribute="bottom" constant="25" id="pHk-Px-kU1"/>
                            <constraint firstItem="bCp-Oq-8UW" firstAttribute="trailing" secondItem="5e9-L7-7YT" secondAttribute="trailing" constant="69" id="uRI-G5-ktm"/>
                            <constraint firstItem="lff-B7-2kH" firstAttribute="leading" secondItem="bCp-Oq-8UW" secondAttribute="leading" constant="30" id="y3q-cr-2il"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="3PY-tp-TX9" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="647.82608695652175" y="-1244.1964285714284"/>
        </scene>
        <!--Onboarding3-->
        <scene sceneID="Vmc-Ot-ZS4">
            <objects>
                <viewController storyboardIdentifier="page3" title="Onboarding3" id="afm-Vy-S38" userLabel="Onboarding3" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="2dn-Qe-sIT">
                        <rect key="frame" x="0.0" y="0.0" width="430" height="932"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dek-Ws-2Q9" customClass="GradientView" customModule="Runner" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="430" height="932"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                        <color key="value" red="0.66273659470000001" green="0.89506334070000004" blue="0.429769069" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                        <color key="value" red="0.**********" green="0.98821324109999997" blue="0.65401792530000002" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isHorizontal" value="NO"/>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="LoveSync" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BaI-fQ-5FH">
                                <rect key="frame" x="30" y="60" width="79" height="21"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pC6-AI-mBa">
                                <rect key="frame" x="331" y="54" width="49" height="33"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                <state key="normal" title="Finish">
                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                            </button>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" image="thirdPageImage" translatesAutoresizingMaskIntoConstraints="NO" id="GmM-cs-TNY">
                                <rect key="frame" x="30" y="106" width="331" height="507"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="Know when your moods are matched &amp; never miss a chance for romance." textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="OaR-I5-v5X">
                                <rect key="frame" x="30" y="806" width="350" height="62"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="62" id="nhk-qq-Kro"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="Be sexually fulfilled" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zEj-1p-Xpd">
                                <rect key="frame" x="30" y="648" width="328" height="150"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="wnO-hA-IKF"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="DINCondensed-Bold" family="DIN Condensed" pointSize="70"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="74s-Lg-XaF"/>
                        <color key="backgroundColor" red="1" green="0.60111642279999999" blue="0.39108792549999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                        <constraints>
                            <constraint firstItem="OaR-I5-v5X" firstAttribute="top" secondItem="zEj-1p-Xpd" secondAttribute="bottom" constant="8" id="5UK-oj-kQE"/>
                            <constraint firstItem="GmM-cs-TNY" firstAttribute="top" secondItem="BaI-fQ-5FH" secondAttribute="bottom" constant="25" id="84x-13-azO"/>
                            <constraint firstItem="BaI-fQ-5FH" firstAttribute="top" secondItem="2dn-Qe-sIT" secondAttribute="top" constant="60" id="8EW-49-qn6"/>
                            <constraint firstItem="pC6-AI-mBa" firstAttribute="centerY" secondItem="BaI-fQ-5FH" secondAttribute="centerY" id="Ab5-Qk-VhY"/>
                            <constraint firstItem="BaI-fQ-5FH" firstAttribute="leading" secondItem="74s-Lg-XaF" secondAttribute="leading" constant="30" id="BCF-SY-wCd"/>
                            <constraint firstItem="74s-Lg-XaF" firstAttribute="trailing" secondItem="OaR-I5-v5X" secondAttribute="trailing" constant="50" id="It1-Dc-ccx"/>
                            <constraint firstItem="dek-Ws-2Q9" firstAttribute="leading" secondItem="74s-Lg-XaF" secondAttribute="leading" id="Kju-hj-LeU"/>
                            <constraint firstItem="zEj-1p-Xpd" firstAttribute="leading" secondItem="GmM-cs-TNY" secondAttribute="leading" id="Q2g-KZ-3UJ"/>
                            <constraint firstItem="zEj-1p-Xpd" firstAttribute="top" secondItem="GmM-cs-TNY" secondAttribute="bottom" constant="35" id="XF5-AM-L0a"/>
                            <constraint firstItem="zEj-1p-Xpd" firstAttribute="leading" secondItem="OaR-I5-v5X" secondAttribute="leading" id="XyU-8G-VMe"/>
                            <constraint firstItem="dek-Ws-2Q9" firstAttribute="trailing" secondItem="74s-Lg-XaF" secondAttribute="trailing" id="ZwI-Ar-TIH"/>
                            <constraint firstItem="74s-Lg-XaF" firstAttribute="trailing" secondItem="GmM-cs-TNY" secondAttribute="trailing" constant="69" id="aKc-9v-vob"/>
                            <constraint firstItem="GmM-cs-TNY" firstAttribute="leading" secondItem="74s-Lg-XaF" secondAttribute="leading" constant="30" id="bE7-i4-xgm"/>
                            <constraint firstItem="dek-Ws-2Q9" firstAttribute="top" secondItem="2dn-Qe-sIT" secondAttribute="top" id="coV-Bd-6Vt"/>
                            <constraint firstAttribute="bottom" secondItem="dek-Ws-2Q9" secondAttribute="bottom" id="dWX-Ja-taw"/>
                            <constraint firstItem="74s-Lg-XaF" firstAttribute="bottom" secondItem="OaR-I5-v5X" secondAttribute="bottom" constant="30" id="i70-gy-xoZ"/>
                            <constraint firstItem="74s-Lg-XaF" firstAttribute="trailing" secondItem="zEj-1p-Xpd" secondAttribute="trailing" constant="72" id="iPq-mF-znV"/>
                            <constraint firstItem="74s-Lg-XaF" firstAttribute="trailing" secondItem="pC6-AI-mBa" secondAttribute="trailing" constant="50" id="ou7-v2-22b"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="LeQ-oE-m0i" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1276.7441860465117" y="-1244.4206008583692"/>
        </scene>
    </scenes>
    <resources>
        <image name="firstPageImage" width="256" height="291"/>
        <image name="secondPageImage" width="256" height="283"/>
        <image name="thirdPageImage" width="256" height="222"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
