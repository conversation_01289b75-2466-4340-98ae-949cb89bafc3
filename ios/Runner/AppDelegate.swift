import UIKit
import Flutter
import FBSDKCoreKit
import StoreKit

@main
@objc class AppDelegate: FlutterAppDelegate, SKProductsRequestDelegate {
    func productsRequest(_ request: SKProductsRequest, didReceive response: SKProductsResponse) {
        
    }
    
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
//     self.window.makeSecure()
    let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
      
      let promoCodeChannel = FlutterMethodChannel(name: "promoCodeChannel", binaryMessenger: controller.binaryMessenger)
              promoCodeChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
                  if call.method == "addPromoCode", let promoCode = call.arguments as? String {
                      let productIdentifier = "Subscription" // Replace with your actual product identifier
                      
                      // Handle promo code addition here
                      if #available(iOS 12.2, *) {
                          if #available(iOS 14.0, *) {
                              SKPaymentQueue.default().presentCodeRedemptionSheet()
                          } else {
                              // Fallback on earlier versions
                          }
                          let productIdentifiers: Set<String> = [productIdentifier]
                          let productsRequest = SKProductsRequest(productIdentifiers: productIdentifiers)
                          productsRequest.delegate = self // Implement SKProductsRequestDelegate methods to handle the response
                          productsRequest.start()
                          
                          result(nil)
                      } else {
                          result(FlutterError(code: "PROMO_CODE_NOT_SUPPORTED", message: "Promo codes are not supported on this device.", details: nil))
                      }
                  }
              }
             

      
    let onboardingChannel = FlutterMethodChannel(name: "lovesync/onboarding", binaryMessenger: controller.binaryMessenger)
    onboardingChannel.setMethodCallHandler({
     (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
        
        let onboardingController: OnboardingViewController = OnboardingViewController()
        onboardingController.modalPresentationStyle = .fullScreen
        onboardingController.onDoneBlock = { res in
            result(0)
        }
        controller.present(onboardingController, animated: true, completion: nil)
      
    })
      
    
    SwiftFlutterNativeDialogPlugin.register(with: controller.registrar(forPlugin: "lovesync/dialog")!)
    
    UIApplication.shared.setStatusBarStyle(UIStatusBarStyle.lightContent, animated: false)
      GeneratedPluginRegistrant.register(with: self)
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
    }
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
    
    override func applicationDidBecomeActive(_ application: UIApplication) {
      //AppEvents.activateApp()
    }
    
//
   
  
  
}
// extension UIWindow {
//   func makeSecure() {
//       let field = UITextField()
//       field.isSecureTextEntry = true
//       self.addSubview(field)
//       field.centerYAnchor.constraint(equalTo: self.centerYAnchor).isActive = true
//       field.centerXAnchor.constraint(equalTo: self.centerXAnchor).isActive = true
//       self.layer.superlayer?.addSublayer(field.layer)
//       field.layer.sublayers?.first?.addSublayer(self.layer)
//     }
//   }

