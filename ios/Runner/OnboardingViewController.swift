//
//  OnboardingViewController.swift
//  Runner
//
//  Created by <PERSON> on 3/15/20.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

import UIKit
import liquid_swipe


class OnboardingViewController: LiquidSwipeContainerController, LiquidSwipeContainerDataSource, LiquidSwipeContainerDelegate {

    var onDoneBlock : ((Bool) -> Void)?

    @objc func skip(_ sender: UIButton) {
        self.dismiss(animated: true, completion: nil)
        onDoneBlock!(true)
    }
    
    var viewControllers: [UIViewController] = {
        var controllers: [UIViewController] = []
        
        let firstPageVC = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "page1")
        let secondPageVC = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "page2")
        let thirdPageVC = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "page3")
        
        controllers.append(firstPageVC)
        controllers.append(secondPageVC)
        controllers.append(thirdPageVC)
        return controllers
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        datasource = self
        delegate = self
        
        viewControllers.forEach { vc in
            let gesture = UITapGestureRecognizer(target: self, action: #selector (self.skip (_:)))
            let buttons = vc.view.subviews.filter{$0 is UIButton}
            buttons[0].addGestureRecognizer(gesture)
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        UIApplication.shared.statusBarStyle = UIStatusBarStyle.default
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        UIApplication.shared.statusBarStyle = UIStatusBarStyle.lightContent
    }

    func numberOfControllersInLiquidSwipeContainer(_ liquidSwipeContainer: LiquidSwipeContainerController) -> Int {
        return viewControllers.count
    }
    
    func liquidSwipeContainer(_ liquidSwipeContainer: LiquidSwipeContainerController, viewControllerAtIndex index: Int) -> UIViewController {
        return viewControllers[index]
    }
    
    func liquidSwipeContainer(_ liquidSwipeContainer: LiquidSwipeContainerController, willTransitionTo: UIViewController) {
        
    }
    
    func liquidSwipeContainer(_ liquidSwipeContainer: LiquidSwipeContainerController, didFinishTransitionTo: UIViewController, transitionCompleted: Bool) {
    }
}
