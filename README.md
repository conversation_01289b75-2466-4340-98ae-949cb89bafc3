# LoveSync

## API Routes Used

| Route                              | Method   | Authenticate | Params     | Body                                                     |
| ---------------------------------- | -------- | ------------ | ---------- | -------------------------------------------------------- |
| `/v1/invitations/cancel_invite`    | `DELETE` | true         | -          | -                                                        |
| `/v1/auth/update`                  | `POST`   | true         | -          | `image`                                                  |
| `/v1/auth/change_password`         | `PUT`    | true         | -          | `current_password`, `password`                           |
| `/v1/auth/update`                  | `POST`   | true         | -          | `username`                                               |
| `/v1/sync_times/reset`             | `POST`   | true         | -          | `sync_id`                                                |
| `/v1/sync_times/sent`              | `GET`    | true         | `id`       | -                                                        |
| `/v1/invitations/send_invite`      | `POST`   | true         | -          | `email`                                                  |
| `/v1/auth/sign_in`                 | `POST`   | false        | -          | `email`, `password`                                      |
| `/v1/invitations/get_partner`      | `GET`    | true         | -          | -                                                        |
| `/v1/invitations/received_invites` | `GET`    | true         | -          | -                                                        |
| `/v1/auth/register_device`         | `PUT`    | true         | -          | `device_id`, `firebase_token`                            |
| `/v1/auth/sign_up`                 | `POST`   | false        | -          | `email`, `username`, `password`, `password_confirmation` |
| `/v1/auth/resend_confirm`          | `POST`   | false        | -          | `email`                                                  |
| `/v1/auth/reset_password_request`  | `POST`   | false        | -          | `email`                                                  |
| `/v1/invitations/sent_invites`     | `GET`    | true         | -          | -                                                        |
| `/v1/sync_times/sent_active`       | `GET`    | true         | -          | -                                                        |
| `/v1/sync_times`                   | `POST`   | true         | -          | `partner_id`, `time`                                     |
| `/v1/invitations/unmatch`          | `DELETE` | true         | `user_id`  | -                                                        |
| `/v1/invitations`                  | `PUT`    | true         | `response` | `inviter_id`                                             |

The general response JSON message is:

```json
{
  "status": 200,
  "message": "Response message or error message",
  "data": {}
}
```

## Making API Requests

```dart
final service = locator<APIService>();
final Response response = await service.request(Request());
```

In order to make a custom request to the server you need to extend the `APIRequest` class (below), set the `R` generic to whatever type you want (if needed) and include external required variables through the constructor (if needed).

```dart
class APIRequest<R> {
  bool get useAuth => false;
  String get path => "/";
  String get method => "GET";
  Map<String, String> get body => null;

  Future<R> getResponse(Map<String, dynamic> json) async {
    return null;
  }
}
```

Below you will find the description for each class property and method:

- `useAuth`: mark as true if you want the service to include the authentication token in the headers (which is manually saved in the local storage after logging in)
- `path`: the API route path (`/v1` is already included) - you can also include variables (`/login?email=$id`)
- `method`: the method used for the API request (`GET`, `POST`, `PUT`, `DELETE`)
- `body`: the body of the API request (only for `POST` and `PUT`)
- `getResponse`: convert the received JSON response to an actual class (`R`)

**Note**: The service will automatically `throw` if the received `status` is not between 200 and 299 (the error message will be a `String`, the `message` JSON field).

## Android Signing

- Add .jks file to `/android/app` directory
- Add a key.properties file with the necessary definitions: `storePassword`, `keyPassword`, `keyAlias`, `storeFile`
- Build via `flutter build appbundle`


## iOS Building and Signing

- Coming