import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:lovesync/analytics.dart';
import 'package:lovesync/api/service.dart';
import 'package:shared_preferences/shared_preferences.dart';

GetIt locator = GetIt.I;

Future<void> setupLocator() async {
  locator.registerLazySingleton(() => APIService());
  locator.registerLazySingleton(() => Analytics());

  WidgetsFlutterBinding.ensureInitialized();

  SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
  locator.registerSingleton<SharedPreferences>(sharedPreferences);
}
