import 'package:flutter/cupertino.dart';
import 'package:purchases_flutter/models/package_wrapper.dart';

class SubscriptionProvider extends ChangeNotifier {
  bool entitlementIsActive = false;

  setEntitlement(bool value) {
    entitlementIsActive = value;
    notifyListeners();
  }
}

class UpdateList extends ChangeNotifier {
  List<bool> selectedBoolValue = [];
  List<Package> myProductList = [];
  List<Package> selectedProduct = [];
  int selectedIndex = 0;

  selectedPlanList() {
    selectedBoolValue = List<bool>.generate(myProductList.length, (index) {
      if (index == 0) {
        selectedIndex = 0;
        selectedProduct = [myProductList[0]];
        return true;
      } else {
        return false;
      }
    });
  }

  updatedPlanList(int index, bool value) {
    selectedBoolValue = List<bool>.generate(myProductList.length, (_) => false);
    selectedBoolValue[index] = value;
    selectedIndex = index;
    selectedProduct = [myProductList[index]];
    notifyListeners();
  }

  productData(Package productData) {
    selectedProduct = [productData];
    notifyListeners();
  }
}

// class UpdateList extends ChangeNotifier {
//   List<bool> selectedBoolValue = [];
//   List<Package> myProductList = [];
//   List<Package> selectedProduct = [];

//   selectedPlanList() {
//     selectedBoolValue = List<bool>.generate(3, (index) {
//       if (index == 1) {
//         return true;
//       } else {
//         return false;
//       }
//     });
//   }

//   updatedPlanList(int index, bool value) {
//     selectedBoolValue = List<bool>.generate(3, (index) {
//       return false;
//     });
//     selectedBoolValue[index] = value;
//     notifyListeners();
//   }

//   productData(Package productData) {
//     selectedProduct.clear();
//     selectedProduct.add(productData);
//     notifyListeners();
//   }
//   //
//   // addPlanEventToFirebase(String identifier) {
//   //   if (identifier == 'face26_1y_3999') {
//   //     FirebaseAnalyticsService().logEvent(AnalyticsEvent.purchaseOneYearPlan,
//   //         parameters: {'uniqueId': uniqueId});
//   //   } else if (identifier == 'face26_1m_899') {
//   //     FirebaseAnalyticsService().logEvent(AnalyticsEvent.purchaseOneMonthPlan,
//   //         parameters: {'uniqueId': uniqueId});
//   //   } else if (identifier == 'face26_1m_free_899') {
//   //     FirebaseAnalyticsService().logEvent(AnalyticsEvent.purchaseFreeTrailPlan,
//   //         parameters: {'uniqueId': uniqueId});
//   //   } else {
//   //     FirebaseAnalyticsService().logEvent(AnalyticsEvent.purchaseOneYearPlan,
//   //         parameters: {'uniqueId': uniqueId});
//   //   }
//   // }
//   //
//   // activeSubscriptionDetail(String identifier) async {
//   //   ActiveSubscriptionDetail subscriptionDetail;
//   //   if (identifier == 'face26_1y_3999') {
//   //     subscriptionDetail = ActiveSubscriptionDetail(
//   //         myProductList[1].storeProduct.description.toString());
//   //     box.put(activeSubscription, subscriptionDetail);
//   //   } else if (identifier == 'face26_1m_899') {
//   //     subscriptionDetail = ActiveSubscriptionDetail(
//   //         myProductList[0].storeProduct.description.toString());
//   //     box.put(activeSubscription, subscriptionDetail);
//   //   } else if (identifier == 'face26_1m_free_899') {
//   //     subscriptionDetail = ActiveSubscriptionDetail(
//   //         myProductList[2].storeProduct.description.toString());
//   //     box.put(activeSubscription, subscriptionDetail);
//   //   } else {
//   //     subscriptionDetail = ActiveSubscriptionDetail(
//   //         myProductList[1].storeProduct.description.toString());
//   //     box.put(activeSubscription, subscriptionDetail);
//   //   }
//   //   notifyListeners();
//   // }
// }
