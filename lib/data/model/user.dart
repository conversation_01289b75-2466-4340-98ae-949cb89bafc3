import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:lovesync/analytics.dart';
import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/locator.dart';
import 'package:shared_preferences/shared_preferences.dart';

class UserModel with ChangeNotifier {
  late String? token;
  UserResponse data = UserResponse();
  UserResponse data1 = UserResponse();
  late int index;

  bool get exists => data != null && data.id != null;
  bool get isLoggedIn => token != null && (token ?? '').isNotEmpty;

  UserModel() {
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    token = sharedPreferences.getString('token');
    if (sharedPreferences.containsKey('user')) {
      String? userJsonString = sharedPreferences.getString('user');
      Map<String, dynamic> partnerJson = jsonDecode(userJsonString!);
      data = UserResponse.fromJSON(partnerJson);
      print("email =====> ${data.isSyncUnable}");
    } else if(sharedPreferences.containsKey('user_is_sync'))
    {
      String? userJsonString1 = sharedPreferences.getString('user_is_sync');
      Map<String, dynamic> partnerJson1 = jsonDecode(userJsonString1!);
      data1 = UserResponse.fromJSON(partnerJson1);
      print("email =====> ${data1.isSyncUnable}");
    }
    else if (sharedPreferences.containsKey('user_id')) {
      data = UserResponse(
        id: sharedPreferences.getInt('user_id') ?? 0,
        email: sharedPreferences.getString('user_email') ?? '',
        username: sharedPreferences.getString('user_username') ?? '',
        image: sharedPreferences.getString('user_image') ?? '',
        successfulSyncPercentage: sharedPreferences.getInt('user_perctenage') ?? 0,
        // successful_sync_percentage: sharedPreferences.getDouble('user_perctenage') ?? 0,
        syncWithSuccess: sharedPreferences.getInt('sync_with_success') ?? 0,
        // sync_with_success: sharedPreferences.getDouble('sync_with_success') ?? 0,
        isSyncUnable: sharedPreferences.getBool('is_sync_unable') ?? false,
        isPremium: sharedPreferences.getBool('is_premium') ?? false,
        startDate: sharedPreferences.getString('created_at') ?? '',
        subSubscriptionDate: sharedPreferences.getString('created_at') ?? '',
        resetDate: sharedPreferences.getString('updated_at') ?? '',
        isPartnerSyncUnable: sharedPreferences.getBool('is_partner_sync_unable') ?? false,
        resetSyncScoreDate: sharedPreferences.getString('start_sync_score_date') ?? '',
        premiumName: sharedPreferences.getString('expiry_plan') ?? '',
      );
      print("date ===> ${sharedPreferences.getString('created_at') ?? ''}");

      _clearOldDataFormat(sharedPreferences, data);
    }
    _setUserAnalytics();
  }

  void _clearOldDataFormat(SharedPreferences sharedPreferences, UserResponse data) async {
    await sharedPreferences.setString('user', jsonEncode(data.toJSON()));
    await sharedPreferences.remove('user_id');
    await sharedPreferences.remove('user_email');
    await sharedPreferences.remove('user_username');
    await sharedPreferences.remove('user_image');
  }

  void _setUserAnalytics() {
    print("_setUserAnalytics called : ${data.email}");
    Analytics analytics = locator<Analytics>();
    print("_setUserAnalytics called1 : ${data.email}");
    if (data != null) {
      print("_setUserAnalytics called3 : ${data.email}");
      analytics.setUser(data.id, data.email);
    } else {
      print("_setUserAnalytics called4 : ${data.email}");
      analytics.setUser(-1, null);
    }
  }

  Future<void> setToken(String? data) async {
    if (token != data) {
      SharedPreferences sharedPreferences = locator<SharedPreferences>();
      await sharedPreferences.setString('token', data ?? "");
      token = data;
      notifyListeners();
    }
  }

  Future<void> setData(UserResponse? user,{bool isSyncUnable = false}) async {
    if (data != user) {
      SharedPreferences sharedPreferences = locator<SharedPreferences>();
      if (user == null) {
        await sharedPreferences.remove('user');
      }  else{
        await sharedPreferences.setString('user', jsonEncode(user.toJSON()));
        print("isSyncUnable  =====> $isSyncUnable");
        if(isSyncUnable == true) {
          await sharedPreferences.setString('user_is_sync', jsonEncode(user.toJSON()));
          data1 = user;
          print("syncScoreDate =======> ${data1.isSyncUnable}");
        }
      }
      data = user ?? UserResponse();


      _setUserAnalytics();
      notifyListeners();
    }
  }
}
