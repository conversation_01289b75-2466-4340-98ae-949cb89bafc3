import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/locator.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum PartnerModelState {
  linked,
  invited,
  invitation,
  twoInvited,
}

class PartnerModel with ChangeNotifier {
  PartnerModelState? state;
  UserResponse? data;
  UserResponse? data1;

  PartnerModel() {
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    if (sharedPreferences.containsKey('partner_state')) {
      print("partner_state is available");
      int partnerStateIndex = sharedPreferences.getInt('partner_state') ?? -1;
      state = PartnerModelState.values[partnerStateIndex];
    }

    if (sharedPreferences.containsKey('partner')) {
      String partnerJsonString = sharedPreferences.getString('partner') ?? '';
      Map<String, dynamic> partnerJson = jsonDecode(partnerJsonString);
      data = UserResponse.fromJSON(partnerJson);
    }

    if (sharedPreferences.containsKey('sentInvite')) {
      String partnerJsonString1 = sharedPreferences.getString('sentInvite') ?? '';
      Map<String, dynamic> partnerJson1 = jsonDecode(partnerJsonString1);
      data1 = UserResponse.fromJSON(partnerJson1);
      print("email =====> ${data1?.email}");
    }
  }

  bool get isLinked => state == PartnerModelState.linked;
  bool get isInvited => state == PartnerModelState.invited;
  bool get isRequesting => state == PartnerModelState.invitation;
  bool get isTwoInvite => state == PartnerModelState.twoInvited;

  Future<void> setState(PartnerModelState? partnerModelState) async {

    if (state != partnerModelState) {
      SharedPreferences sharedPreferences = locator<SharedPreferences>();
      if (partnerModelState == null) {
        await sharedPreferences.remove('partner_state');
      } else {
        await sharedPreferences.setInt('partner_state', partnerModelState.index);
      }
      state = partnerModelState;
      log('set-state1 $state');
      notifyListeners();
    }
  }

  Future<void> setData(UserResponse? user, {bool isSentInvite = false}) async {
    if (data != user) {
      SharedPreferences sharedPreferences = locator<SharedPreferences>();
      if (user == null) {
        await sharedPreferences.remove('partner');
      } else {
        await sharedPreferences.setString('partner', jsonEncode(user.toJSON()));
        print("isSentInvite $isSentInvite");
        if(isSentInvite == true) {
          await sharedPreferences.setString('sentInvite', jsonEncode(user.toJSON()));
          data1 = user;
          log('set-state1 ${data1?.email}');
        }
        //await sharedPreferences.setString('receiveInvite', jsonEncode(user.toJSON()));

      }
      data = user;
      }
      notifyListeners();
    }
  }

