import 'package:flutter/material.dart';
import 'package:lovesync/locator.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppModel with ChangeNotifier {
  late bool _isFirstLaunch;
  late bool _isTutorialFinished;
  late bool _hasAskedRate;
  late DateTime? _firstLaunchTime;

  AppModel() {
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    if (sharedPreferences.containsKey('first_launch')) {
      _isFirstLaunch = sharedPreferences.getBool('first_launch') ?? false;
    } else {
      _isFirstLaunch = true;
    }

    if (sharedPreferences.containsKey('tutorial_finished')) {
      _isTutorialFinished = sharedPreferences.getBool('tutorial_finished') ?? false;
    } else {
      _isTutorialFinished = false;
    }

    if (sharedPreferences.containsKey('has_asked_rate')) {
      _hasAskedRate = sharedPreferences.getBool('has_asked_rate') ?? false;
    } else {
      _hasAskedRate = false;
    }

    if (sharedPreferences.containsKey('first_launch_time')) {
      _firstLaunchTime = DateTime.parse(sharedPreferences.getString('first_launch_time') ?? '');
    } else {
      _firstLaunchTime = null;
    }
  }

  bool get isFirstLaunch => _isFirstLaunch;
  bool get isTutorialFinished => _isTutorialFinished;
  bool get hasAskedRate => _hasAskedRate;
  DateTime? get firstLaunchTime => _firstLaunchTime;

  Future<void> setNotFirstLaunch() async {
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    await sharedPreferences.setBool('first_launch', false);
    _isFirstLaunch = false;
    notifyListeners();
  }

  Future<void> setTutorialFinished() async {
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    await sharedPreferences.setBool('tutorial_finished', true);
    _isTutorialFinished = true;
    notifyListeners();
  }

  Future<void> setHasAskedRate(bool value) async {
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    await sharedPreferences.setBool('has_asked_rate', value);
    _hasAskedRate = value;
    notifyListeners();
  }

  Future<void> setFirstLaunch() async {
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    DateTime launchTime = DateTime.now();
    await sharedPreferences.setString('first_launch_time', launchTime.toIso8601String());
    _firstLaunchTime = launchTime;
    notifyListeners();
  }
}
