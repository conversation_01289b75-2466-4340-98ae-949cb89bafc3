import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:lovesync/analytics.dart';
import 'package:lovesync/api/responses/sync.dart';
import 'package:lovesync/locator.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SyncModel with ChangeNotifier {
  SyncResponse? sentActive;
  late int _syncsSinceAskedRate;
  late int _lastSyncId;

  SyncModel() {
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    if (sharedPreferences.containsKey('sync')) {
      String? syncJsonString = sharedPreferences.getString('sync');
      Map<String, dynamic> syncJson = jsonDecode(syncJsonString!);
      sentActive = SyncResponse.fromJSON(syncJson);
      print("get active =====> $sentActive");
    }

    if (sharedPreferences.containsKey('syncs_since_asked_rate')) {
      _syncsSinceAskedRate = sharedPreferences.getInt('syncs_since_asked_rate') ?? 0;
    } else {
      _syncsSinceAskedRate = 0;
    }

    if (sharedPreferences.containsKey('last_sync_id')) {
      _lastSyncId = sharedPreferences.getInt('last_sync_id') ?? 0;
    } else {
      _lastSyncId = 0;
    }
  }

  bool get exists => sentActive != null && sentActive?.id != null;

  bool get waitingSync{
    if (sentActive != null && sentActive!.expiresAt.isAfter(DateTime.now())) {
      return true;
    }
    return false;
  }

  bool get synced {
    if (sentActive != null) {
      return sentActive!.isSync;
    }
    return false;
  }

  int get syncsSinceAskedRate => _syncsSinceAskedRate;
  int get lastSyncId => _lastSyncId;

  Future<void> setSentActive(SyncResponse? syncSent, int userId) async {
    if (sentActive != syncSent) {
      if (sentActive != null && syncSent != null && sentActive?.id == syncSent.id && sentActive?.isSync == false && syncSent.isSync == true) {
        Analytics analytics = locator<Analytics>();
        analytics.syncConfirmed(userId, syncSent.id);

        await setLastSyncId(syncSent.id);
        await setSyncsSinceAskedRate(_syncsSinceAskedRate + 1);
      }

      SharedPreferences sharedPreferences = locator<SharedPreferences>();
      if (syncSent == null) {
        await sharedPreferences.remove('sync');
      } else {
        await sharedPreferences.setString('sync', jsonEncode(syncSent.toJSON()));
      }
      sentActive = syncSent;
      print("sent active setString=====> ${sentActive?.isSync}");
      print("sent active setString=====> ${sentActive?.id}");

     notifyListeners();
    }
  }

  Future<void> setSyncsSinceAskedRate(int value) async {
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    await sharedPreferences.setInt('syncs_since_asked_rate', value);
    _syncsSinceAskedRate = value;
  }

  Future<void> setLastSyncId(int value) async {
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    await sharedPreferences.setInt('last_sync_id', value);
    _lastSyncId = value;
  }
}
