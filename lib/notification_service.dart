import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationServices {
  static var notification = FlutterLocalNotificationsPlugin();
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  var initializationSettingsAndroid = const AndroidInitializationSettings('@mipmap/ic_launcher');
  var initializationSettingsDarwin = const DarwinInitializationSettings(
      // requestAlertPermission: true,
      // requestBadgePermission: true,
      // requestSoundPermission: true,
      );

  Future<void> initialiseNotification() async {
    var initializationSettings = InitializationSettings(android: initializationSettingsAndroid, iOS: initializationSettingsDarwin);
    await flutterLocalNotificationsPlugin.initialize(initializationSettings);
  }



  Future<void> showNotification({required int id, required String title, required String body,String payLoad = ''}) async {

    await flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'id',
          'channel',
          //importance: Importance.min,
          // ongoing: true,
          styleInformation: BigTextStyleInformation(''),
          icon: '@mipmap/logo',
        ),
        iOS: DarwinNotificationDetails(),
      ),
      payload: payLoad,
    );
  }
}
