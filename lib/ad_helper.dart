import 'dart:io';

class AdHelper {

  static String get bannerAdUnitId {
    if (Platform.isAndroid) {
     //return "ca-app-pub-8511393645043978/9062998349";
      return "ca-app-pub-8511393645043978/5449290037";


    } else if (Platform.isIOS) {
     //return "ca-app-pub-8511393645043978/9062998349";
     return "ca-app-pub-8511393645043978/2113460503";

    } else {
      throw UnsupportedError("Unsupported platform");
    }
  }

  static String get nativeAdUnitId {
    if (Platform.isAndroid) {
      return "<YOUR_ANDROID_NATIVE_AD_UNIT_ID>";
    } else if (Platform.isIOS) {
      return "<YOUR_IOS_NATIVE_AD_UNIT_ID>";
    } else {
      throw UnsupportedError("Unsupported platform");
    }
  }
}