import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:lovesync/analytics.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/data/model/app.dart';
import 'package:lovesync/data/model/partner.dart';
import 'package:lovesync/data/model/sync.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/purchase_setup/purchase_api.dart';
import 'package:lovesync/purchase_setup/singletons_data.dart';
import 'package:lovesync/purchase_setup/store_config.dart';
import 'package:lovesync/purchase_setup/store_config.dart' as config;
import 'package:lovesync/purchase_setup/subscritpion_provider.dart';
import 'package:lovesync/router.dart';
import 'package:provider/provider.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'notification_service.dart';

SharedPreferences? prefs;
final navKey = GlobalKey<NavigatorState>();
BuildContext? globalContext;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Register the method channel
  MobileAds.instance.initialize();
  MethodChannel _channel = MethodChannel('promoCodeChannel');

  await Firebase.initializeApp();
  prefs = await SharedPreferences.getInstance();
  if (Platform.isIOS) {
    StoreConfig(
      store: config.Store.appleStore,
      apiKey: appleApiKey,
    );
  } else if (Platform.isAndroid) {
    StoreConfig(
      store: config.Store.googlePlay,
      apiKey: googleApiKey,
    );
  }

  // WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
  //   if(Platform.isAndroid){
  //     await FlutterWindowManager.addFlags(FlutterWindowManager.FLAG_SECURE);
  //   }
  // });

  NotificationServices notificationServices = NotificationServices();
  await notificationServices.initialiseNotification();

  //await Upgrader.clearSavedSettings();

  await setupLocator();
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider<AppModel>(create: (_) => AppModel()),
        ChangeNotifierProvider<UserModel>(create: (_) => UserModel()),
        ChangeNotifierProvider<PartnerModel>(create: (_) => PartnerModel()),
        ChangeNotifierProvider<SyncModel>(create: (_) => SyncModel()),
        ChangeNotifierProvider<UpdateList>(create: (_) => UpdateList()),
        ChangeNotifierProvider<SubscriptionProvider>(
            create: (_) => SubscriptionProvider()),
      ],
      child: LoveSyncApp(),
    ),
  );
}

Future<dynamic> _handleMethodCall(MethodCall call) {
  // Handle method calls from the platform if needed
  return Future.value(null);
}

class LoveSyncApp extends StatefulWidget {
  LoveSyncApp({Key? key}) : super(key: key);

  @override
  _LoveSyncAppState createState() => _LoveSyncAppState();
}

class _LoveSyncAppState extends State<LoveSyncApp> {
  final InAppReview _inAppReview = InAppReview.instance;

  @override
  void initState() {
    super.initState();
    onboarding();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      AppModel appModel = Provider.of<AppModel>(context, listen: false);
      SyncModel syncModel = Provider.of<SyncModel>(context, listen: false);

      if (appModel.firstLaunchTime == null) {
        appModel.setFirstLaunch();
        return;
      }

      if (await _inAppReview.isAvailable() &&
          appModel.firstLaunchTime!
              .isBefore(DateTime.now().subtract(Duration(days: 2)))) {
        if (!appModel.hasAskedRate && syncModel.syncsSinceAskedRate >= 2) {
          appModel.setHasAskedRate(true);
          syncModel.setSyncsSinceAskedRate(0);
          _inAppReview.requestReview();
          return;
        }

        if (appModel.hasAskedRate && syncModel.syncsSinceAskedRate >= 3) {
          appModel.setHasAskedRate(true);
          syncModel.setSyncsSinceAskedRate(0);
          _inAppReview.requestReview();
          return;
        }
      }
      await initPlatformState();
    });
  }

  Future<void> initPlatformState() async {
    final userModel = Provider.of<UserModel>(context, listen: false);

    // await Purchases.setDebugLogsEnabled(true);
    await Purchases.setLogLevel(LogLevel.debug);

    PurchasesConfiguration configuration;
    configuration = PurchasesConfiguration(StoreConfig.instance!.apiKey)
      ..appUserID = userModel.data.email;
    // ..observerMode = false;
    await Purchases.configure(configuration);

    CustomerInfo customerInfo = await Purchases.getCustomerInfo();
    print("customerInfo : ${customerInfo.entitlements}");
    print("customerInfo : ${customerInfo.allPurchaseDates}");
    print("customerInfo : ${customerInfo.activeSubscriptions}");

    appData.appUserID = customerInfo.originalAppUserId;

    Offerings offerings = await Purchases.getOfferings();

    Purchases.addCustomerInfoUpdateListener((customerInfo1) async {
      appData.appUserID = customerInfo1.originalAppUserId;

      CustomerInfo customerInfo = await Purchases.getCustomerInfo();
      print("customer data ====> ${customerInfo.allPurchaseDates}");
      print("customer data ====> ${customerInfo.entitlements}");
      print("customer data ====> ${customerInfo.allExpirationDates}");
      print("customer data ====> ${customerInfo.activeSubscriptions}");
      Offerings offerings = await Purchases.getOfferings();
      var myProductList = offerings.current?.availablePackages;
      print("Product length : $myProductList");

      final valueProvider = Provider.of<UpdateList>(context, listen: false);
      valueProvider.myProductList = myProductList!;

      print("Provider : ${valueProvider.myProductList[0]}");

      (customerInfo.entitlements.all[entitlementID] != null &&
              customerInfo.entitlements.all[entitlementID]!.isActive)
          // ? subscriptionProvider.setEntitlement(true)
          // : subscriptionProvider.setEntitlement(false);
          ? appData.entitlementIsActive = true
          : appData.entitlementIsActive = false;
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      navigatorKey: navKey,
      title: 'LoveSync',
      theme: ThemeData(
        scaffoldBackgroundColor: colorBackground,
        dialogBackgroundColor: colorBackground,
        textTheme: Typography(platform: defaultTargetPlatform).white.apply(),
      ),
      initialRoute: RouteName.Home,
      onGenerateRoute: LSRouter.generateRoute,
    );
  }

  void onboarding() async {
    AppModel appModel = Provider.of<AppModel>(context, listen: false);
    if (appModel.isFirstLaunch) {
      MethodChannel methodChannel = MethodChannel("lovesync/onboarding");
      await methodChannel.invokeMethod("launch");
      appModel.setNotFirstLaunch();

      Analytics analytics = locator<Analytics>();
      analytics.onboardingDone();
    }
  }
}
