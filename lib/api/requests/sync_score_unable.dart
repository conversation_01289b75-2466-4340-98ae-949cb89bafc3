import 'package:lovesync/api/service.dart';

import '../responses/user.dart';

class SyncScoreUnableRequest extends APIRequest<UserResponse>{
  bool isSyncAble;


  SyncScoreUnableRequest(this.isSyncAble);

  @override
  bool get useAuth => true;

  @override
  String get path => "settings/is_sync_unable?is_sync_unable=$isSyncAble";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
    "isSyncAble" : isSyncAble.toString(),

  };

Future<UserResponse> getResponse(Map<String, dynamic> json) async {
  return UserResponse.fromJSON(json["data"]);
}
}
