import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';

class ChangeAvatarRequest extends APIRequest<UserResponse> {

  String base64;

  ChangeAvatarRequest(this.base64);

  @override
  bool get useAuth => true;

  @override
  String get path => "auth/update";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
    "image": base64,
  };

  Future<UserResponse> getResponse(Map<String, dynamic> json) async {
    return UserResponse.fromJSON(json["data"]);
  }
}
