import 'package:lovesync/api/service.dart';

class ChangePasswordRequest extends APIRequest {

  String current;
  String password;

  ChangePasswordRequest(this.current, this.password);

  @override
  bool get useAuth => true;

  @override
  String get path => "auth/change_password";

  @override
  String get method => "PUT";

  @override
  Map<String, String> get body => {
    "current_password": current,
    "password": password,
  };
}
