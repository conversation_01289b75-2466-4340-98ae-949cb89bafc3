import 'package:lovesync/api/service.dart';

class UpdateInviteResponse {

  static const String Accept = "accept";
  static const String Reject = "reject";
}

class UpdateInviteRequest extends APIRequest {

  int id;
  String response;

  UpdateInviteRequest(this.id, this.response);

  @override
  bool get useAuth => true;

  @override
  String get path => "invitations/$response";

  @override
  String get method => "PUT";

  @override
  Map<String, String> get body => {
    "inviter_id": id.toString(),
  };
}
