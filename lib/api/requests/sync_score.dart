import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';

class SyncScoreRequest extends APIRequest<UserResponse> {


  SyncScoreRequest();

  @override
  bool get useAuth => true;

  @override
  String get path => "sync_times/successful_sync_percentage";

  @override
  String get method => "GET";

  // @override
  // Map<String, String> get body => {
  //   "id" : id.toString(),
  //   "message": message,
  // };

Future<UserResponse> getResponse(Map<String, dynamic> json) async {
  return UserResponse.fromJSON(json["data"]);
}
}
