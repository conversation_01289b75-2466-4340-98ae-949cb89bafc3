import 'package:lovesync/api/responses/sync.dart';
import 'package:lovesync/api/service.dart';

class StartSyncRequest extends APIRequest<SyncResponse> {
  int id;

  StartSyncRequest(this.id);

  @override
  bool get useAuth => true;

  @override
  String get path => "sync_times/start";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
    "sync_id": id.toString(),
  };

  Future<SyncResponse> getResponse(Map<String, dynamic> json) async {
    return SyncResponse.fromJSON(json["data"]);
  }
}
