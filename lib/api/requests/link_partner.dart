import 'package:lovesync/api/service.dart';

class LinkPartnerRequest extends APIRequest {
  String email;

  LinkPartnerRequest(this.email);

  @override
  bool get useAuth => true;

  @override
  String get path => "invitations/send_invite";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
        "email": email,
      };

  Future<dynamic> getResponse(Map<String, dynamic> json) async {
    return json["message"];
  }
}
