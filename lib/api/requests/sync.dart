import 'package:flutter/material.dart';
import 'package:lovesync/api/responses/sync.dart';
import 'package:lovesync/api/service.dart';

import '../../main.dart';
import '../../router.dart';
import '../../ui/widget/common_button.dart';

class SyncRequest extends APIRequest<SyncResponse> {
  int id;
  int minutes;
 
  bool delayStart; int count;

  SyncRequest(
    this.id,
    this.minutes,
    this.delayStart,this.count,
  );

  @override
  bool get useAuth => true;

  @override
  String get path => "sync_times";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
        "partner_id": id.toString(),
        "time": minutes.toString(),
        "delay_start": delayStart.toString(),
        "update_count":count.toString(),
      };

  Future<SyncResponse> getResponse(Map<String, dynamic> json) async {
    print("message month======> ${json['message']}");
    if (json['message'] == 'This month sync score is finished!!') {
      await showDialog(
          context: globalContext!,
          builder: (BuildContext context) {
            print("message month======> ${json['message']}");
            return AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(15))),
              contentPadding: EdgeInsets.only(top: 12.0, bottom: 10.0),
              backgroundColor: Colors.white,
              title: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                            //Future.delayed(Duration(microseconds: 200))..then((value) => _durationText = '...');
                          },
                          child: Icon(
                            Icons.cancel,
                            color: Colors.grey,
                          )),
                    ],
                  ),
                  Container(
                    height: 80,
                    width: 80,
                    decoration: BoxDecoration(image: DecorationImage(image: AssetImage('assets/yellow_circle.png'))),
                    child: Center(
                        child: Image.asset(
                      'assets/premium_icon.png',
                      height: 45,
                      width: 45,
                    )),
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Center(
                    child: Text(
                      'You have used your 3 free syncs this month',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black),
                    ),
                  ),
                ],
              ),
              content: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Center(
                            child: Text(
                          "You will receive another 3 syncs next month",
                          style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w700, fontSize: 15),
                        )),
                        SizedBox(
                          height: 15,
                        ),
                        Center(
                            child: Text(
                          "For Unlimited Syncs and other great benefits Upgrade to LSLoveSync+",
                          style: TextStyle(
                              color: Colors.black.withOpacity(0.8), fontWeight: FontWeight.w500, fontSize: 16),
                        )),
                      ],
                    )),
              ),
              actions: [
                StatefulBuilder(
                  builder: (BuildContext context, void Function(void Function()) setState) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 17),
                      child: CommonButton(
                          onPressed: () {
                            Navigator.pushNamed(context, RouteName.SubscriptionPlan)
                              ..then((value) {
                                setState(
                                  () {
                                    // _durationText = '...';
                                  },
                                );
                              });
                          },
                          name: 'Upgrade'),
                    );
                  },
                ),
                SizedBox(
                  height: 10,
                ),
              ],
            );
          });
      return SyncResponse.fromJSON(json["data"]);
    } else {
      return SyncResponse.fromJSON(json["data"]);
    }
  }
}
