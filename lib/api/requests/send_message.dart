import 'package:http/http.dart' as http;
import 'package:lovesync/api/service.dart';

class SendMessageRequest extends APIRequest {
  final String message;
  final int id;
  //final List<Uint8List> images;

  SendMessageRequest(this.message, this.id, );

  @override
  bool get useAuth => true;

  @override
  String get path => "messages?message[message]=$message&message[receiver_id]=$id";

  @override
  String get method => "POST";

  Map<String, String> get headers => {
    'Content-type': 'multipart/form-data',
  };

  @override
  Map<String, String> get body => {
    "message": message,
    "id": id.toString()
  };

  @override
  http.MultipartRequest? get multipartRequest {
    final request = http.MultipartRequest(method, Uri.parse(path));
    print("request ====> $request");
    request.headers.addAll(headers);
    request.fields['message'] = message;
    request.fields['receiver_id'] = id.toString();
    // for (int i = 0; i < images.length; i++) {
    //   request.files.add(http.MultipartFile.fromBytes(
    //     'images',
    //     images[i],
    //     filename: 'image_$i.jpg',
    //   ));
    //   print("request ====> ${images[i]}");
    //   //return request;
    // }
   // return request;
  }
}

// Usage

