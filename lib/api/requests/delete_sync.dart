// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:lovesync/api/service.dart';

class DeleteSyncRequest extends APIRequest {
  int id;
  bool isExpire;
  String waitTime;

  DeleteSyncRequest(
    this.id, {
    this.isExpire = false,
    required this.waitTime
  });

  @override
  bool get useAuth => true;

  @override
  String get path => "sync_times/reset";

  @override
  String get method => "POST";

  @override
  Map<String, dynamic> get body => {
        "sync_id": id.toString(),
        "is_expire": isExpire.toString(),
        "wait_time": waitTime.toString()
      };
}
