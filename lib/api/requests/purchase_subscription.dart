import 'package:lovesync/api/service.dart';

class PurchaseSubscriptionRequest extends APIRequest {
  final String plan;
  final int time;

  PurchaseSubscriptionRequest(this.plan,this.time);

  @override
  bool get useAuth => true;

  @override
  String get path => "subscribed?plan_id=$plan&time=$time";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
    "plan" : plan,
    "time": time.toString(),
  };

// Future<SyncResponse> getResponse(Map<String, dynamic> json) async {
//   return SyncResponse.fromJSON(json["data"]);
// }
}

