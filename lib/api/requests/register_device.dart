import 'package:lovesync/api/service.dart';

class RegisterDeviceRequest extends APIRequest {

  String id;
  String token;

  RegisterDeviceRequest(this.id, this.token);

  @override
  bool get useAuth => true;

  @override
  String get path => "auth/register_device";

  @override
  String get method => "PUT";

  @override
  Map<String, String> get body => {
    "device_id": id,
    "firebase_token": token
  };
}
