import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';

class ChangeUsernameRequest extends APIRequest<UserResponse> {

  String username;

  ChangeUsernameRequest(this.username);

  @override
  bool get useAuth => true;

  @override
  String get path => "auth/update";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
    "username": username,
  };

  Future<UserResponse> getResponse(Map<String, dynamic> json) async {
    return UserResponse.fromJSON(json["data"]);
  }
}
