import 'package:lovesync/api/service.dart';

class DeleteAccountRequest extends APIRequest<Map<String, dynamic>> {
  @override
  bool get useAuth => true;

  @override
  String get method => "DELETE";

  @override
  String get path => "auth/delete_account";

  Future<Map<String, dynamic>> getResponse(Map<String, dynamic> json) async {
    print("data ===> ${json["data"]}");
    return json["data"];
  }
}
