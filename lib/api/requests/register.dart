import 'package:lovesync/api/service.dart';

class RegisterRequest extends APIRequest<String> {

  String email;
  String username;
  String password;

  RegisterRequest(this.email, this.username, this.password);

  @override
  String get path => "auth/sign_up";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
    "email": email,
    "username": username,
    "password": password,
    "password_confirmation": password,
  };

  Future<String> getResponse(Map<String, dynamic> json) async {
    return json["message"];
  }
}
