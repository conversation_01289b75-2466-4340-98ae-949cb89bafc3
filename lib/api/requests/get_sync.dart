import 'package:lovesync/api/responses/sync.dart';
import 'package:lovesync/api/service.dart';

class GetSyncRequest extends APIRequest<SyncResponse> {
  int id;

  GetSyncRequest(this.id);

  @override
  bool get useAuth => true;

  @override
  String get path => "sync_times/sent/$id";

  Future<SyncResponse> getResponse(Map<String, dynamic> json) async {
    return SyncResponse.fromJSON(json["data"]);
  }
}
