import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';

class SubscriptionDetailRequest extends APIRequest<UserResponse> {

  SubscriptionDetailRequest();

  @override
  bool get useAuth => true;

  @override
  String get path => "sync_times/:user_id/plan_info";

  @override
  String get method => "GET";

  // @override
  // Map<String, String> get body => {
  //   "id" : id.toString(),
  //   "message": message,
  // };

  Future<UserResponse> getResponse(Map<String, dynamic> json) async {
    return UserResponse.fromJSON(json["data"]);
  }
}
