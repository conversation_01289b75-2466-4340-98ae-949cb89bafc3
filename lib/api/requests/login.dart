import 'package:lovesync/api/responses/login.dart';
import 'package:lovesync/api/service.dart';

class LoginRequest extends APIRequest<LoginResponse> {
  String email;
  String password;

  LoginRequest(this.email, this.password);

  @override
  String get path => "auth/sign_in";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
        "email": email,
        "password": password,
      };

  Future<LoginResponse> getResponse(Map<String, dynamic> json) async {
    print("data ===> ${json["data"]}");
    return LoginResponse.fromJSON(json["data"]);
  }
}
