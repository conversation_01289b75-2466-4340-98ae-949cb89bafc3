import 'package:lovesync/api/service.dart';

import '../responses/user.dart';

class ResetSyncScoreRequest extends APIRequest<UserResponse>{
  int id;


  ResetSyncScoreRequest(this.id);

  @override
  bool get useAuth => true;

  @override
  String get path => "settings/:$id/reset_sync_score_percentage";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
    "id" : id.toString(),

  };

  Future<UserResponse> getResponse(Map<String, dynamic> json) async {
    return UserResponse.fromJSON(json["data"]);
  }
}
