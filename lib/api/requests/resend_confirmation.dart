import 'package:lovesync/api/service.dart';

class ResendConfirmationRequest extends APIRequest<String> {

  String email;

  ResendConfirmationRequest(this.email);

  @override
  String get path => "auth/resend_confirm";

  @override
  String get method => "POST";

  @override
  Map<String, String> get body => {
    "email": email,
  };

  Future<String> getResponse(Map<String, dynamic> json) async {
    return json["message"];
  }
}
