import 'package:equatable/equatable.dart';

class SyncResponse extends Equatable {
  final int id;
  final bool isSync;
  final String minutes;
  final bool started;
  final DateTime startsAt;
  final DateTime expiresAt;
  final String status;
  final String? message;
  final int syncedId;

  SyncResponse( {
    this.message = '',
    required this.id,
    required this.isSync,
    required this.minutes,
    required this.started,
    required this.startsAt,
    required this.expiresAt,
    required this.status,
    required this.syncedId,
  });

  @override
  List<Object?> get props => [id, isSync, minutes, started, startsAt, expiresAt, status, syncedId,message];

  Map<String, dynamic> toJSON() {
    var output = Map<String, dynamic>();
    output['id'] = id;
    output['is_sync'] = isSync;
    output['wait_time'] = minutes;
    output['started'] = started;
    output['start_at'] = startsAt.toIso8601String();
    output['expired_at'] = expiresAt.toIso8601String();
    output['status'] = status;
    output['message'] = message;
    output['synced_id'] = syncedId;
    return output;
  }

  factory SyncResponse.fromJSON(Map<String, dynamic> json) {
    bool started;
    if (json.containsKey('started') && json['started'] != null) {
      started = json['started'];
    } else {
      started = true;
    }

    String startsAt;
    if (json.containsKey('created_at')) {
      startsAt = json['created_at'];
    } else {
      startsAt = json['start_at'];
    }

    int syncedId;
    if (json.containsKey('synced_id')) {
      syncedId = json['synced_id'];
    } else {
      syncedId = 0;
    }

    return SyncResponse(
      id: json['id'],
      isSync: json['is_sync'],
      minutes: json['wait_time'],
      started: started,
      startsAt: DateTime.parse(startsAt),
      expiresAt: DateTime.parse(json['expired_at']),
      status: json['status'],
      message:  json['message'] ?? '',
      syncedId: syncedId,
    );
  }
}
