import 'package:equatable/equatable.dart';

class UserResponse extends Equatable {
  final int id;
  // final double? successful_sync_percentage;
  final dynamic successfulSyncPercentage;
  // final double? sync_with_success;
  final int? syncWithSuccess;
  final int? previousSyncScore;
  final bool? isSyncUnable;
  final bool? isPartnerSyncUnable;
  final bool? isPremium;
  final String email;
  final String username;
  final String? image;
  final String? startDate;
  final String? resetDate;
  final String? subSubscriptionDate;
  final String? resetSyncScoreDate;

  final String? premiumName;

  UserResponse({
    this.resetSyncScoreDate,
    this.previousSyncScore,
    this.startDate,
    this.resetDate,
    this.premiumName = '',
    this.syncWithSuccess,
    this.isPartnerSyncUnable,
    this.id = 0,
    this.successfulSyncPercentage = 0,
    this.subSubscriptionDate,
    this.isSyncUnable,
    this.isPremium,
    this.email = '',
    this.username = 'test',
    this.image = '',
  });

  @override
  List<Object?> get props => [
        subSubscriptionDate,
        previousSyncScore,
        resetSyncScoreDate,
        resetDate,
        syncWithSuccess,
        id,
        email,
        username,
        image,
        successfulSyncPercentage,
        isSyncUnable,
        isPremium,
        startDate,
        premiumName,
      ];

  Map<String, dynamic> toJSON() {
    var output = Map<String, dynamic>();
    output['id'] = id;
    output['email'] = email;
    output['username'] = username;
    output['sync_score'] = {'successful_sync_percentage': successfulSyncPercentage};
    output['sync_score'] = {'start_at': resetSyncScoreDate};
    output['plan_name'] = premiumName;
    output['previous_sync_score'] = previousSyncScore;
    output['is_sync_unable'] = isSyncUnable;
    output['sync_with_success'] = syncWithSuccess;
    output['created_at'] = startDate;
    output['updated_at'] = resetDate;
    output['expiry_plan'] = subSubscriptionDate;
    output['is_premium'] = isPremium;
    output['image'] = {'url': image};
    output['partner'] = {'is_sync_unable': isPartnerSyncUnable};
    return output;
  }

  factory UserResponse.fromJSON(Map<String, dynamic> json) {
    return UserResponse(
      id: json['id'] ?? 0,
      email: json['email'] ?? '',
      username: json['username'] ?? 'test', //
      successfulSyncPercentage:
          json.containsKey('sync_score') ? json['sync_score']['successful_sync_percentage'] : null,
      resetSyncScoreDate: json.containsKey('sync_score') ? json['sync_score']['start_at'] : null,
      premiumName: json['plan_name'] ?? '',
      syncWithSuccess: json.containsKey('sync_with_success') ? json['sync_with_success'] : null,
      previousSyncScore: json['previous_sync_score'] ?? 0,
      isSyncUnable: json['is_sync_unable'],
      isPremium: json['is_premium'],
      startDate: json['created_at'],
      subSubscriptionDate: json['expiry_plan'],
      resetDate: json['updated_at'],
      image: json.containsKey('image') && json['image'] != null ? "${json['image']['url']}" : '',
      isPartnerSyncUnable:
          json.containsKey('partner') && json['partner'] != null && json['partner'].containsKey('is_sync_unable')
              ? json['partner']['is_sync_unable']
              : null,
    );
  }
}
