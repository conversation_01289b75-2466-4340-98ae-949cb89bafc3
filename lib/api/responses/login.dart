import 'package:equatable/equatable.dart';
import 'package:lovesync/api/responses/user.dart';

class LoginResponse extends Equatable {
  final UserResponse? user;
  final String? token;

  LoginResponse({
    this.user,
    this.token,
  });

  @override
  List<Object?> get props => [user, token];

  factory LoginResponse.fromJSON(Map<String, dynamic> json) {
    return LoginResponse(
      user: UserResponse.fromJSON(json),
      token: json['token'],
    );
  }
}
