import 'dart:convert';
import 'dart:developer';

import 'package:http/http.dart';
import 'package:lovesync/config.dart';
import 'package:lovesync/locator.dart';
import 'package:mutex/mutex.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../ui/screen/screen_button.dart';
import '../ui/screen/screen_partner.dart';
import '../view/subscription_plan.dart';

class APIRequest<R> {
  bool get useAuth => false;
  String get path => "/";
  String get method => "GET";
  Map<String, dynamic>? get body => null;
  MultipartRequest? get multipartRequest => null;
  //FormData?  get formData => null;

  Future<R?> getResponse(Map<String, dynamic> json) async {
    return null;
  }
}

class APIService {
  static final String baseURL = Config.apiUrl;
  static final String baseSocket = Config.apiSocket;

  final client = Client();
  final mutex = Mutex();

  Future<R?> request<R, T extends APIRequest<R>>(T req) async {
    await mutex.acquire();

    try {
      // String url = "$baseURL/${req.path}";
      String url = "$baseURL/${req.path}";
      print("request =====> ${req.path}");
      //print("request =====> ${url}");
      //print("request =====> ${ButtonScreen.customMessage}}");
      //print("request =====> $url");
      if (req.path == 'auth/sign_in' ||
          req.path == 'sync_times/:user_id/plan_info' ||
          req.path == 'subscribed?plan_id=${SubscriptionPlanScreen.plan}&time=${SubscriptionPlanScreen.time}' ||
          req.path ==
              'messages?message[message]=${ButtonScreen.customMessage.isNotEmpty ? ButtonScreen.customMessage : ButtonScreen.drpDownValues}&message[receiver_id]=${ButtonScreen.userid}' ||
          req.path == 'sync_times' ||
          req.path == 'sync_times/successful_sync_percentage' ||
          req.path == 'settings/is_sync_unable?is_sync_unable=${PartnerScreen.isSyncScore}' ||
          req.path == 'settings/:${PartnerScreen.id}/reset_sync_score_percentage') {
        //  url = "${Config.apiUrl1}/${req.path}";
        // url = "http://***********:3001/v2/${req.path}";
        url = "https://api.lovesync.com/v2/${req.path}";
        // print("v1 url=====>$url");
        // print("v1 url=====>${Config.apiUrl1}/${req.path}");
      } else {
        url = "$baseURL/${req.path}";
        //print("v1 =====>");
      }

      Map<String, String> headers = {};

      if (req.useAuth) {
        SharedPreferences sharedPreferences = locator<SharedPreferences>();
        final token = sharedPreferences.getString('token');

        if (token == null || token.isEmpty) {
          throw ("Token is empty");
        }

        headers["Authorization"] = "Bearer $token";

        print("token ===> Bearer $token");
      }

      Response response;

      switch (req.method) {
        case "GET":
          print("url get ====> ${Uri.parse(url)}");
          print("body ====> ${req.body}");
          response = await client.get(Uri.parse(url), headers: headers);
          log("GET API response  ====> ${response.body}");
          break;
        case "POST":
          print("url post ====> ${Uri.parse(url)}");
          print("body post ====> ${req.body}");
          print("body header ====> $headers");
          response = await client.post(Uri.parse(url), headers: headers, body: req.body);
          print("response post  ====> ${response.body}");
          break;

        case "PUT":
          // print("url put ====> ${Uri.parse(url)}");
          // print("body ====> ${req.body}");
          response = await client.put(Uri.parse(url), headers: headers, body: req.body);
          print("response put  ====> ${response.body}");
          break;

        case "DELETE":
          // print("url delete ====> ${Uri.parse(url)}");
          // print("body ====> ${req.body}");
          response = await client.delete(Uri.parse(url), headers: headers);
          print("response delete ====> ${response.body}");
          break;
        default:
          throw ("Not implemented");
      }

      if (response.statusCode < 200 || response.statusCode > 299) {
        throw ("Status not 200");
      }

      Map<String, dynamic> body = json.decode(response.body);
      if (body["status"] < 200 || body["status"] > 299) {
        throw (body["message"] ?? "Status not 200");
      }

      return await req.getResponse(body);
    } finally {
      mutex.release();
    }
  }
}
