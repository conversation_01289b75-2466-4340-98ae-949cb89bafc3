import 'package:flutter/material.dart';
import 'package:lovesync/view/changePassword.dart';
import 'package:lovesync/view/changeUsername.dart';
import 'package:lovesync/view/home.dart';
import 'package:lovesync/view/linkPartner.dart';
import 'package:lovesync/view/login.dart';
import 'package:lovesync/view/register.dart';
import 'package:lovesync/view/requestSent.dart';
import 'package:lovesync/view/resetPassword.dart';
import 'package:lovesync/view/subscription_plan.dart';

class RouteName {
  static const String Login = "login";
  static const String ResetPassword = "resetPassword";
  static const String Register = "register";
  static const String LinkPartner = "linkPartner";
  static const String RequestSent = "requestSent";
  static const String Home = "home";
  static const String ChangePassword = "changePassword";
  static const String ChangeUsername = "changeUsername";
  static const String SubscriptionPlan = "SubscriptionPlan";
  static const String SendImageMessage = "SendImageMessage";
}

class LSRouter {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case RouteName.Login:
        return MaterialPageRoute(builder: (_) => LoginView());
      case RouteName.ResetPassword:
        return MaterialPageRoute(builder: (_) => ResetPasswordView());
      case RouteName.Register:
        String email = '';
        if (settings.arguments != null) {
          print("passed data ==> ${settings.arguments.toString()}");
          Map args1 = settings.arguments as Map;
          String? args = args1['email'].toString();
          if (args != null) {
            email = args;
          }
        }
        return MaterialPageRoute(builder: (_) => RegisterView(email: email));
      case RouteName.LinkPartner:
        return MaterialPageRoute(builder: (_) => LinkPartner());
      case RouteName.RequestSent:
        return MaterialPageRoute(builder: (_) => RequestSent());
      case RouteName.Home:
        return MaterialPageRoute(builder: (_) => HomeView());
      case RouteName.ChangePassword:
        return MaterialPageRoute(builder: (_) => ChangePasswordView());
      case RouteName.ChangeUsername:
        return MaterialPageRoute(builder: (_) => ChangeUsernameView());
      case RouteName.SubscriptionPlan:
        return MaterialPageRoute(builder: (_) => SubscriptionPlanScreen());
      default:
        throw ("No route defined for ${settings.name}");
    }
  }
}
