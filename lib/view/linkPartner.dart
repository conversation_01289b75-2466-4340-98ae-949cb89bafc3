import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lovesync/api/requests/link_partner.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/router.dart';
import 'package:lovesync/ui/widget/button.dart';
import 'package:provider/provider.dart';

import '../analytics.dart';

class LinkPartner extends StatefulWidget {
  LinkPartner({Key? key}) : super(key: key);

  @override
  LinkPartnerState createState() => LinkPartnerState();
}

class LinkPartnerState extends State<LinkPartner> {
  String _errorMessage = "";

  final emailController = TextEditingController();

  void _onSubmit() async {
    HapticFeedback.lightImpact();

    String email = emailController.text.trim();

    setState(() {
      _errorMessage = "";
    });

    APIService service = locator<APIService>();

    try {
      final response = await service.request(LinkPartnerRequest(email));

      print("response ============++> $response");

      if (response == "Invitation sent successfully." || response == "invitation sent successfully") {
        UserModel userModel = Provider.of<UserModel>(context, listen: false);
        Analytics analytics = locator<Analytics>();
        analytics.partnerInvited(userModel.data.id, email);

        Navigator.pushNamed(context, RouteName.RequestSent);
      } else {
        setState(() {
          _errorMessage = response;
        });
      }
    } catch (e) {
      setState(() {
        //_errorMessage = e.toString();
        _errorMessage = "Some thing went wrong. Please try again.";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    log("Connect Partner");
    return Scaffold(
        backgroundColor: colorBackground,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Container(
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                colors: [colorPrimary, colorAccent],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              )),
              child: SafeArea(
                  child: Container(
                margin: EdgeInsets.only(top: 80, left: 48, right: 48, bottom: 48),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text("Connect Partner", style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold)),
                    SizedBox(height: 50),
                    Text("Invite your partner to connect with you on LoveSync.",
                        style: TextStyle(
                          fontSize: 16,
                        )),
                  ],
                ),
              )),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(48),
                children: <Widget>[
                  TextField(
                    controller: emailController,
                    decoration: InputDecoration(
                      labelText: 'Partner Email',
                      labelStyle: TextStyle(
                        color: Color(0xFFE5EAED),
                      ),
                      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Color(0xFF5B646C))),
                      focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: colorPrimary)),
                    ),
                  ),
                  Visibility(
                    visible: _errorMessage != null && _errorMessage.isNotEmpty,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(height: 24),
                        Text(
                          _errorMessage,
                          style: TextStyle(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24),
                  Button(
                    'Connect!',
                    theme: Button.THEME_DEFAULT,
                    onTap: () {
                      _onSubmit();
                    },
                  ),
                  SizedBox(height: 12),
                  Button(
                    "Cancel",
                    theme: Button.THEME_DEFAULT,
                    onTap: () {
                      Navigator.pushReplacementNamed(context, RouteName.Home);
                    },
                  ),
                ],
              ),
            )
          ],
        ));
  }
}
