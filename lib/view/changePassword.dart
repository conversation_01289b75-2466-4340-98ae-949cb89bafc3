import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lovesync/api/requests/change_password.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/ui/widget/button.dart';


class ChangePasswordView extends StatefulWidget {
  ChangePasswordView({Key? key}) : super(key: key);

  @override
  ChangePasswordViewState createState() => ChangePasswordViewState();
}

class ChangePasswordViewState extends State<ChangePasswordView> {
  String _errorMessage = "";

  final currentController = TextEditingController();
  final passwordController = TextEditingController();
  final repeatController = TextEditingController();

  void _onSubmit() async {
    HapticFeedback.lightImpact();

    String current = currentController.text;
    String password = passwordController.text;
    String repeat = repeatController.text;

    setState(() {
      _errorMessage = "";
    });

    APIService service = locator<APIService>();

    try {
      if (password != repeat) throw ("Passwords don't match");

      await service.request(ChangePasswordRequest(current, password));

      Navigator.pop(context);
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: colorBackground,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [colorPrimary, colorAccent],
                  begin: Alignment.bottomLeft,
                  end: Alignment.topRight,
                ),
              ),
              child: SafeArea(
                  child: Container(
                margin: EdgeInsets.only(top: 12, left: 12, right: 12, bottom: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Align(
                      alignment: Alignment.topLeft,
                      child: IconButton(
                        icon: Icon(Icons.arrow_back),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ),
                  ],
                ),
              )),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(48),
                children: <Widget>[
                  TextField(
                    controller: currentController,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: 'Current Password',
                      labelStyle: TextStyle(
                        color: Color(0xFFE5EAED),
                      ),
                      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Color(0xFF5B646C))),
                      focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: colorPrimary)),
                    ),
                  ),
                  SizedBox(height: 12),
                  TextField(
                    controller: passwordController,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: 'New Password',
                      labelStyle: TextStyle(
                        color: Color(0xFFE5EAED),
                      ),
                      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Color(0xFF5B646C))),
                      focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: colorPrimary)),
                    ),
                  ),
                  SizedBox(height: 12),
                  TextField(
                    controller: repeatController,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: 'Repeat Password',
                      labelStyle: TextStyle(
                        color: Color(0xFFE5EAED),
                      ),
                      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Color(0xFF5B646C))),
                      focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: colorPrimary)),
                    ),
                  ),
                  Visibility(
                    visible: _errorMessage != null && _errorMessage.isNotEmpty,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(height: 24),
                        Text(
                          _errorMessage,
                          style: TextStyle(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24),
                  Button(
                    "Change Password",
                    theme: Button.THEME_POSITIVE,
                    onTap: () {
                      _onSubmit();
                    },
                  ),

                ],
              ),
            )
          ],
        ));
  }
}
