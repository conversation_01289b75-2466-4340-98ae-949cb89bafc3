import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lovesync/api/requests/change_username.dart';
import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/ui/widget/button.dart';
import 'package:provider/provider.dart';

class ChangeUsernameView extends StatefulWidget {
  ChangeUsernameView({Key? key}) : super(key: key);

  @override
  ChangeUsernameViewState createState() => ChangeUsernameViewState();
}

class ChangeUsernameViewState extends State<ChangeUsernameView> {
  String _errorMessage = "";

  final usernameController = TextEditingController();

  @override
  void initState() {
    super.initState();

    final userModel = Provider.of<UserModel>(context, listen: false);
    usernameController.text = userModel.data.username;
  }

  void _onSubmit() async {
    HapticFeedback.lightImpact();

    String username = usernameController.text;

    setState(() {
      _errorMessage = "";
    });

    APIService service = locator<APIService>();

    try {
      UserResponse newUser = await service.request(ChangeUsernameRequest(username));

      final userModel = Provider.of<UserModel>(context, listen: false);
      await userModel.setData(newUser);

      Navigator.pop(context);
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: colorBackground,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [colorPrimary, colorAccent],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              ),
            ),
            child: SafeArea(
                child: Container(
              margin: EdgeInsets.only(top: 12, left: 12, right: 12, bottom: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Align(
                    alignment: Alignment.topLeft,
                    child: IconButton(
                      icon: Icon(Icons.arrow_back),
                      onPressed: () {
                        Navigator.pop(context);
                      },
                    ),
                  ),
                ],
              ),
            )),
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.all(48),
              children: <Widget>[
                TextField(
                  controller: usernameController,
                  decoration: InputDecoration(
                    labelText: 'Display Name',
                    labelStyle: TextStyle(
                      color: Color(0xFFE5EAED),
                    ),
                    enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Color(0xFF5B646C))),
                    focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: colorPrimary)),
                  ),
                ),
                Visibility(
                  visible: _errorMessage.isNotEmpty,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 24),
                      Text(
                        _errorMessage,
                        style: TextStyle(color: Colors.red),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 24),
                Button(
                  "Change Display Name",
                  theme: Button.THEME_POSITIVE,
                  onTap: () {
                    _onSubmit();
                  },
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
