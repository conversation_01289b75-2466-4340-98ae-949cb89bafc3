import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lovesync/api/requests/reset_password.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/ui/widget/alert.dart';
import 'package:lovesync/ui/widget/button.dart';
import 'package:provider/provider.dart';

class ResetPasswordView extends StatefulWidget {
  ResetPasswordView({Key? key}) : super(key: key);

  @override
  ResetPasswordViewState createState() => ResetPasswordViewState();
}

class ResetPasswordViewState extends State<ResetPasswordView> {
  String _errorMessage = "";

  final emailController = TextEditingController();

  void _onSubmit(UserModel userModel) async {
    HapticFeedback.lightImpact();

    String email = emailController.text;

    setState(() {
      _errorMessage = "";
    });

    APIService service = locator<APIService>();

    try {
      await service.request(ResetPasswordRequest(email));

      await showDialog(
        builder: (context) => AlertBox(
          title: "Reset Password",
          subtitle: "Your password was reset, check your email for instructions",
        ),
        context: context,
      );

      Navigator.pop(context);
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    UserModel userModel = Provider.of<UserModel>(context, listen: false);

    return Scaffold(
        backgroundColor: colorBackground,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Container(
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                colors: [colorPrimary, colorAccent],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              )),
              child: SafeArea(
                  child: Container(
                margin: EdgeInsets.only(top: 80, left: 48, right: 48, bottom: 48),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text("Reset Password", style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold)),
                    SizedBox(height: 50),
                    Text("Enter your email to reset your account password.",
                        style: TextStyle(
                          fontSize: 16,
                        )),
                  ],
                ),
              )),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(48),
                children: <Widget>[
                  TextField(
                    controller: emailController,
                    decoration: InputDecoration(
                      labelText: 'Email',
                      labelStyle: TextStyle(
                        color: Color(0xFFE5EAED),
                      ),
                      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Color(0xFF5B646C))),
                      focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: colorPrimary)),
                    ),
                  ),
                  Visibility(
                    visible: _errorMessage.isNotEmpty,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(height: 24),
                        Text(
                          _errorMessage,
                          style: TextStyle(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24),
                  Button(
                    'Reset',
                    theme: Button.THEME_POSITIVE,
                    onTap: () {
                      _onSubmit(userModel);
                    },
                  ),
                  SizedBox(height: 12),
                  Button(
                    "Cancel",
                    theme: Button.THEME_DEFAULT,
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                ],
              ),
            )
          ],
        ));
  }
}
