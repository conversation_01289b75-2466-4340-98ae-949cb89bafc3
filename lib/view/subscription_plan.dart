import 'package:flutter/material.dart';
import 'package:lovesync/purchase_setup/purchase_api.dart';
import 'package:lovesync/purchase_setup/singletons_data.dart';
import 'package:lovesync/purchase_setup/store_config.dart';
import 'package:lovesync/purchase_setup/subscritpion_provider.dart';
import 'package:lovesync/ui/widget/common_button.dart';
import 'package:provider/provider.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../api/requests/subscription_detail.dart';
import '../api/responses/user.dart';
import '../api/service.dart';
import '../data/model/user.dart';
import '../locator.dart';
import '../router.dart';
import '../ui/widget/common_subscription_plan_widget.dart';

class SubscriptionPlanScreen extends StatefulWidget {
  const SubscriptionPlanScreen({Key? key}) : super(key: key);

  @override
  State<SubscriptionPlanScreen> createState() => _SubscriptionPlanScreenState();
  static int time = 0;
  static String plan = '';
}

class _SubscriptionPlanScreenState extends State<SubscriptionPlanScreen> {
  bool isPremiumActivate = true;
  bool isLoading = false;
  // final MethodChannel _codeRedemptionChannel = MethodChannel('promo_code_channel');
  String promoCode = '';
  ValueNotifier<bool> isProductLoading = ValueNotifier(false);

  // void addPromoCode(String promoCode) {
  //   const promoCodeChannel = MethodChannel('promoCodeChannel');
  //   try {
  //     promoCodeChannel.invokeMethod('addPromoCode', promoCode);
  //   } on PlatformException catch (e) {
  //     // Handle any platform-specific errors
  //     print('Error: $e');
  //   }
  // }

  Future<void> initPlatformState() async {
    isProductLoading.value = true;
    final userModel = Provider.of<UserModel>(context, listen: false);
    await Purchases.setDebugLogsEnabled(true);

    final configuration = PurchasesConfiguration(StoreConfig.instance!.apiKey)
      ..appUserID = userModel.data.email;

    await Purchases.configure(configuration);

    final customerInfo = await Purchases.getCustomerInfo();
    appData.appUserID = customerInfo.originalAppUserId;

    // ✅ Fetch products immediately
    Offerings offerings = await Purchases.getOfferings();

    final myProductList = offerings.current?.availablePackages ?? [];

    if (mounted) {
      final valueProvider = Provider.of<UpdateList>(context, listen: false);
      print('==========>>${myProductList}');
      valueProvider.myProductList = myProductList;
      // valueProvider.selectedPlanList(); // if needed
      setState(() {});
    }

    // ✅ Check entitlement
    appData.entitlementIsActive =
        customerInfo.entitlements.all[entitlementID]?.isActive ?? false;

    // ✅ Listen for any future updates (like after purchase)
    Purchases.addCustomerInfoUpdateListener((updatedCustomerInfo) async {
      appData.appUserID = updatedCustomerInfo.originalAppUserId;
      appData.entitlementIsActive =
          updatedCustomerInfo.entitlements.all[entitlementID]?.isActive ??
              false;

      if (mounted) setState(() {});
    });
    isProductLoading.value = false;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await initPlatformState();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<UpdateList>(builder: (context, value, child) {
        APIService service = locator<APIService>();
        final user = Provider.of<UserModel>(context, listen: false);

        if (!(value.selectedBoolValue.isNotEmpty)) {
          value.selectedPlanList();
        }

        return SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Icon(
                            Icons.arrow_back,
                            color: Colors.white,
                          )),
                      Flexible(
                        child: Center(
                          child: Text(
                            "Upgrade to LoveSync+",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 20),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 15,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * 0.12),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          commonContainer(),
                          SizedBox(
                            width: 7,
                          ),
                          commonName(
                            "Unlimited Syncs",
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          commonContainer(),
                          SizedBox(
                            width: 7,
                          ),
                          commonName(
                            "Up to 12 hr sync desire",
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          commonContainer(),
                          SizedBox(
                            width: 7,
                          ),
                          commonName(
                            "Sync score",
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          commonContainer(),
                          SizedBox(
                            width: 7,
                          ),
                          commonName(
                            "Private messaging",
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          commonContainer(),
                          SizedBox(
                            width: 7,
                          ),
                          commonName(
                            "Remove ads",
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                ValueListenableBuilder(
                    valueListenable: isProductLoading,
                    builder: (context, productLoading, child) {
                      return productLoading
                          ? Center(
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 50),
                                child: CircularProgressIndicator(),
                              ),
                            )
                          : Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // ListView.builder(
                                //   shrinkWrap: true,
                                //   reverse: true,
                                //   primary: false,
                                //   itemCount: value.myProductList.length,
                                //   itemBuilder: (context, index) {
                                //     var totalPriceForYear =
                                //         (value.myProductList[0].storeProduct.price * 12);

                                //     var savePriceForYear = (totalPriceForYear -
                                //         value.myProductList[1].storeProduct.price);

                                //     var percentage =
                                //         (((savePriceForYear / totalPriceForYear) * 100))
                                //             .ceil();

                                //     if (index == 0) {
                                //       return Padding(
                                //           padding:
                                //               EdgeInsets.only(left: 24, right: 24, top: 13),
                                //           child: CustomUpgradedRadioButton(
                                //             months: '12 Month',
                                //             dollar: value
                                //                 .myProductList[1].storeProduct.priceString,
                                //             yearlyOrMonthly: '/ Year',
                                //             isOfferShow: true,
                                //             isFreeTrail: false,
                                //             percentage: '$percentage % Off',
                                //             index: index,
                                //             provider: value,
                                //             onTap: () {
                                //               value.updatedPlanList(index, true);
                                //               value.productData(value.myProductList[1]);
                                //             },
                                //           ));
                                //     } else if (index == 1) {
                                //       return Padding(
                                //           padding: EdgeInsets.symmetric(
                                //               horizontal: 24, vertical: 15),
                                //           child: CustomUpgradedRadioButton(
                                //               months: '1 Month',
                                //               dollar: value.myProductList[0].storeProduct
                                //                   .priceString,
                                //               yearlyOrMonthly: '/ Month',
                                //               isOfferShow: false,
                                //               isFreeTrail: false,
                                //               index: index,
                                //               provider: value,
                                //               onTap: () {
                                //                 print("change color =====>");
                                //                 value.updatedPlanList(index, true);
                                //                 value.productData(value.myProductList[0]);
                                //               }));
                                //     }
                                //   },
                                // ),
                                ListView.builder(
                                  shrinkWrap: true,
                                  reverse: false, // <== Keep it simple
                                  primary: false,
                                  itemCount: value.myProductList.length,
                                  itemBuilder: (context, index) {
                                    final package = value.myProductList[index];

                                    final isYearly = package
                                        .storeProduct.identifier
                                        .contains(
                                            "year"); // or any identifier logic
                                    final totalPriceForYear = (value
                                            .myProductList
                                            .firstWhere((e) => e
                                                .storeProduct.identifier
                                                .contains("month"))
                                            .storeProduct
                                            .price *
                                        12);
                                    final savePrice = totalPriceForYear -
                                        package.storeProduct.price;
                                    final percentage =
                                        ((savePrice / totalPriceForYear) * 100)
                                            .ceil();

                                    return Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 24, vertical: 13),
                                      child: CustomUpgradedRadioButton(
                                        months:
                                            isYearly ? '12 Month' : '1 Month',
                                        dollar:
                                            package.storeProduct.priceString,
                                        yearlyOrMonthly:
                                            isYearly ? '/ Year' : '/ Month',
                                        isOfferShow: isYearly,
                                        isFreeTrail: false,
                                        percentage: isYearly
                                            ? '$percentage % Off'
                                            : null,
                                        index: index,
                                        provider: value,
                                        onTap: () {
                                          value.updatedPlanList(index, true);
                                        },
                                      ),
                                    );
                                  },
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 25, top: 15, right: 25),
                                  child: Column(
                                    children: [
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Icon(
                                            Icons.star,
                                            color: Colors.white70,
                                            size: 8,
                                          ),
                                          SizedBox(
                                            width: 5,
                                          ),
                                          Expanded(
                                            child: Text(
                                              "Upgrade will be automatically applied to connected partner's account",
                                              style: TextStyle(
                                                  color: Colors.white70,
                                                  fontSize: 11,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 40,
                                      ),
                                      CommonButton(
                                          onPressed: () async {
                                            setState(() {
                                              isLoading = true;
                                            });
                                            try {
                                              CustomerInfo customerInfo =
                                                  // await Purchases.purchasePackage(
                                                  //     value.selectedProduct.isEmpty
                                                  //         ? value.myProductList[1]
                                                  //         : value.selectedProduct.first);
                                                  await Purchases
                                                      .purchasePackage(value
                                                              .myProductList[
                                                          value.selectedIndex]);

                                              await showDialog(
                                                  context: context,
                                                  barrierDismissible: false,
                                                  builder:
                                                      (BuildContext context) {
                                                    return AlertDialog(
                                                      shape: RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius.all(
                                                                  Radius
                                                                      .circular(
                                                                          15))),
                                                      contentPadding:
                                                          EdgeInsets.only(
                                                              top: 12.0,
                                                              bottom: 10.0),
                                                      backgroundColor:
                                                          Colors.white,
                                                      title: Column(
                                                        children: [
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .end,
                                                            children: [
                                                              GestureDetector(
                                                                  onTap: () {
                                                                    Navigator.of(
                                                                            context)
                                                                        .pop();
                                                                  },
                                                                  child: Icon(
                                                                    Icons
                                                                        .cancel,
                                                                    color: Colors
                                                                        .grey,
                                                                  )),
                                                            ],
                                                          ),
                                                          Container(
                                                            height: 80,
                                                            width: 80,
                                                            decoration: BoxDecoration(
                                                                image: DecorationImage(
                                                                    image: AssetImage(
                                                                        'assets/circle.png'))),
                                                            child: Image.asset(
                                                                'assets/tick-circle.png'),
                                                          ),
                                                          SizedBox(
                                                            height: 15,
                                                          ),
                                                          Center(
                                                            child: Text(
                                                              'Purchase Successful',
                                                              style: TextStyle(
                                                                  fontSize: 20,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  color: Colors
                                                                      .black),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      content: SizedBox(
                                                        width: MediaQuery.of(
                                                                context)
                                                            .size
                                                            .width,
                                                        child: Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        30),
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                SizedBox(
                                                                  height: 15,
                                                                ),
                                                                Text(
                                                                  "Enjoy the LoveSync+ features",
                                                                  style: TextStyle(
                                                                      color: Colors
                                                                          .black,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w500,
                                                                      fontSize:
                                                                          17),
                                                                ),
                                                                SizedBox(
                                                                  height: 15,
                                                                ),
                                                                Row(
                                                                  children: [
                                                                    Text(
                                                                      "•",
                                                                      style: TextStyle(
                                                                          color: Colors
                                                                              .black,
                                                                          fontWeight: FontWeight
                                                                              .w500,
                                                                          fontSize:
                                                                              20),
                                                                    ),
                                                                    SizedBox(
                                                                      width: 7,
                                                                    ),
                                                                    Text(
                                                                      "Unlimited Syncs",
                                                                      style: TextStyle(
                                                                          color: Colors
                                                                              .black54,
                                                                          fontWeight: FontWeight
                                                                              .w500,
                                                                          fontSize:
                                                                              15),
                                                                    ),
                                                                  ],
                                                                ),
                                                                Row(
                                                                  children: [
                                                                    Text(
                                                                      "•",
                                                                      style: TextStyle(
                                                                          color: Colors
                                                                              .black,
                                                                          fontWeight: FontWeight
                                                                              .w500,
                                                                          fontSize:
                                                                              20),
                                                                    ),
                                                                    SizedBox(
                                                                      width: 7,
                                                                    ),
                                                                    Text(
                                                                      "Up to 12 hr sync desire",
                                                                      style: TextStyle(
                                                                          color: Colors
                                                                              .black54,
                                                                          fontWeight: FontWeight
                                                                              .w500,
                                                                          fontSize:
                                                                              15),
                                                                    ),
                                                                  ],
                                                                ),
                                                                Row(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    Text(
                                                                      "•",
                                                                      style: TextStyle(
                                                                          color: Colors
                                                                              .black,
                                                                          fontWeight: FontWeight
                                                                              .w500,
                                                                          fontSize:
                                                                              20),
                                                                    ),
                                                                    SizedBox(
                                                                      width: 7,
                                                                    ),
                                                                    Flexible(
                                                                        child: Padding(
                                                                            padding: EdgeInsets.only(top: 3),
                                                                            child: Text(
                                                                              "Sync score",
                                                                              style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w500, fontSize: 15),
                                                                            ))),
                                                                  ],
                                                                ),
                                                                Row(
                                                                  children: [
                                                                    Text(
                                                                      "•",
                                                                      style: TextStyle(
                                                                          color: Colors
                                                                              .black,
                                                                          fontWeight: FontWeight
                                                                              .w500,
                                                                          fontSize:
                                                                              20),
                                                                    ),
                                                                    SizedBox(
                                                                      width: 7,
                                                                    ),
                                                                    Flexible(
                                                                        child:
                                                                            Text(
                                                                      "Private messaging",
                                                                      style: TextStyle(
                                                                          color: Colors
                                                                              .black54,
                                                                          fontWeight: FontWeight
                                                                              .w500,
                                                                          fontSize:
                                                                              15),
                                                                    )),
                                                                  ],
                                                                ),
                                                                Row(
                                                                  children: [
                                                                    Text(
                                                                      "•",
                                                                      style: TextStyle(
                                                                          color: Colors
                                                                              .black,
                                                                          fontWeight: FontWeight
                                                                              .w500,
                                                                          fontSize:
                                                                              20),
                                                                    ),
                                                                    SizedBox(
                                                                      width: 7,
                                                                    ),
                                                                    Flexible(
                                                                        child:
                                                                            Text(
                                                                      "Remove ads",
                                                                      style: TextStyle(
                                                                          color: Colors
                                                                              .black54,
                                                                          fontWeight: FontWeight
                                                                              .w500,
                                                                          fontSize:
                                                                              15),
                                                                    )),
                                                                  ],
                                                                ),
                                                              ],
                                                            )),
                                                      ),
                                                      actions: [
                                                        Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal:
                                                                      17),
                                                          child: CommonButton(
                                                              //isProcess: isLoading,
                                                              onPressed:
                                                                  () async {
                                                                print(
                                                                    "premiumName ======> ${user.data.premiumName}");
                                                                final UserResponse
                                                                    userResponse =
                                                                    await service
                                                                        .request(
                                                                            SubscriptionDetailRequest());

                                                                await user
                                                                    .setData(
                                                                        userResponse)
                                                                    .then(
                                                                        (value) {
                                                                  Navigator.pushNamed(
                                                                      context,
                                                                      RouteName
                                                                          .Home);
                                                                });
                                                                // Navigator.pushNamed(context, RouteName.Home);
                                                              },
                                                              name: 'Continue'),
                                                        ),
                                                        SizedBox(
                                                          height: 10,
                                                        ),
                                                      ],
                                                    );
                                                  });

                                              // await PurchaseSubscriptionDialog.subscriptionDialog(context: context);

                                              setState(() {
                                                isLoading = false;
                                              });
                                            } catch (e) {
                                              isPremiumActivate = false;
                                              setState(() {
                                                isLoading = false;
                                              });
                                            }
                                            // addPromoCode('MEDIATRIAl');
                                          },
                                          name: 'Continue',
                                          isProcess: isLoading,
                                          minWidth1: double.infinity),
                                      SizedBox(
                                        height: 20,
                                      ),
                                      GestureDetector(
                                          onTap: () async {
                                            //final appId = '';
                                            final promoCode = '';
                                            //final url = Uri.parse('https://apps.apple.com/redeem?ctx=$appId&id=1483221951&code=$promoCode');
                                            final url = Uri.parse(
                                                'https://apps.apple.com/redeem?ctx=offercodes&id=1483221951&code=$promoCode');

                                            if (await canLaunchUrl(url)) {
                                              await launchUrl(url);
                                            } else
                                              throw "Could not launch $url";
                                          },
                                          child: Center(
                                              child: Text(
                                            "Have a Promo Code?",
                                            style: TextStyle(
                                                color: Colors.grey,
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600),
                                          ))),
                                    ],
                                  ),
                                ),
                              ],
                            );
                    }),
              ],
            ),
          ),
        );
      }),
    );
  }
}

Container commonContainer() {
  return Container(
    decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.grey),
    height: 7,
    width: 7,
  );
}

Text commonName(String title) {
  return Text(
    title,
    style: TextStyle(
        color: Colors.grey, fontSize: 16, fontWeight: FontWeight.w600),
  );
}
