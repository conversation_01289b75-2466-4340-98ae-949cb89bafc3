import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:android_id/android_id.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lovesync/analytics.dart';
import 'package:lovesync/api/cable.dart';
import 'package:lovesync/api/requests/cancel_invite.dart';
import 'package:lovesync/api/requests/delete_sync.dart';
import 'package:lovesync/api/requests/get_sync.dart';
import 'package:lovesync/api/requests/partner.dart';
import 'package:lovesync/api/requests/received_invite.dart';
import 'package:lovesync/api/requests/register_device.dart';
import 'package:lovesync/api/requests/sent_invite.dart';
import 'package:lovesync/api/requests/start_sync.dart';
import 'package:lovesync/api/requests/sync_sent.dart';
import 'package:lovesync/api/requests/unlink_partner.dart';
import 'package:lovesync/api/responses/sync.dart';
import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/data/model/app.dart';
import 'package:lovesync/data/model/partner.dart';
import 'package:lovesync/data/model/sync.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/ui/screen/screen_settings.dart';
import 'package:lovesync/ui/widget/alert.dart';
import 'package:lovesync/ui/widget/toolbar.dart';
import 'package:provider/provider.dart';
import 'package:upgrader/upgrader.dart';

import '../api/requests/delete_account.dart';
import '../api/requests/sign_out.dart';
import '../api/requests/subscription_detail.dart';
import '../api/requests/sync_score.dart';
import '../main.dart';
import '../notification_service.dart';
import '../ui/screen/screen_button.dart';
import '../ui/screen/screen_partner.dart';
import '../ui/widget/custom_dialog.dart';

//test

class HomeView extends StatefulWidget {
  HomeView({Key? key}) : super(key: key);
  static String msgId = "1";
  @override
  HomeViewState createState() => HomeViewState();
}

class HomeViewState extends State<HomeView> with WidgetsBindingObserver {
  late int _page;
  late PageController _controller;
  Timer? _timer;
  String? _token;
  Cable? _cable;
  bool isSelected = false;
  bool _isSentInvite = true;

  late UserModel userModel;

  @override
  void initState() {
    print('HomeView screen inistate');

    super.initState();
    //_startPolling();
    userModel = Provider.of<UserModel>(context, listen: false);
    _page = 1;
    _controller = PageController(initialPage: 1);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      FirebaseMessaging messaging = FirebaseMessaging.instance;
      String? token = await FirebaseMessaging.instance.getToken();
      print('firebase token ------>$token');
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('User granted permission');
      } else if (settings.authorizationStatus ==
          AuthorizationStatus.provisional) {
        print('User granted provisional permission');
      } else {
        print('User declined or has not accepted permission');
      }

      FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
        print("before message ===> ${HomeView.msgId}");
        if (HomeView.msgId != message.messageId) {
          HomeView.msgId = message.messageId ?? "";
          print("message ===> ${message.messageId}");
          print("message ===> ${HomeView.msgId}");
          print(
              "FirebaseMessaging called ==> ${message.notification?.toMap()}");
          NotificationServices notificationServices = NotificationServices();
          await notificationServices.showNotification(
            id: 1,
            title: message.notification?.title ?? "",
            body: message.notification?.body ?? "",
          );
          String payload = message.data['payload'];
          Map<String, dynamic> payloadMap = jsonDecode(payload);
          if (payloadMap.containsKey('data')) {
            dynamic data = payloadMap['data']['message'];
            print('A new onMessageOpenedApp event was published! Data: $data');
            PartnerModel partnerModel = Provider.of<PartnerModel>(
                navKey.currentContext!,
                listen: false);
            String messageBody = message.notification?.body ?? "";
            {
              if ((data ?? '').isNotEmpty) {
                showDialog(
                    context: navKey.currentContext!,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(15))),
                        contentPadding:
                            EdgeInsets.only(top: 12.0, bottom: 10.0),
                        backgroundColor: Colors.white,
                        title: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "From ${partnerModel.data?.username}",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.w500,
                                      fontSize: 18),
                                ),
                                GestureDetector(
                                    onTap: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: Icon(
                                      Icons.cancel,
                                      color: Colors.grey,
                                    )),
                              ],
                            ),
                          ],
                        ),
                        content: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 30),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 15,
                                  ),
                                  Text(
                                    data ?? '',
                                    style: TextStyle(
                                        color: Colors.grey,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 17),
                                  ),
                                  SizedBox(
                                    height: 10,
                                  ),
                                ],
                              )),
                        ),
                      );
                    });
              } else if (messageBody.contains(
                  'your request to partner with ${partnerModel.data?.email} was not accepted')) {
                print('------->notification get =====');
                PartnerModel partnerModel = Provider.of<PartnerModel>(
                    navKey.currentContext!,
                    listen: false);
                final service = locator<APIService>();

                try {
                  await service.request(CancelInviteRequest());
                  await partnerModel.setState(null);
                  await partnerModel.setData(null);
                } catch (err) {
                  print(err);
                }
              }
            }
          }
          print("message ===> ${message.notification?.body}");
          _refresh();
        }
      });

      // on notification click to open app and show dialog
      FirebaseMessaging.onMessageOpenedApp
          .listen((RemoteMessage message) async {
        String payload = message.data['payload'];
        Map<String, dynamic> payloadMap = jsonDecode(payload);
        //  Navigator.pushNamed(context, RouteName.Login);
        if (payloadMap.containsKey('data')) {
          dynamic data = payloadMap['data']['message'];
          print('A new onMessageOpenedApp event was published! Data: $data');
          String messageBody = message.notification?.body ?? "";
          PartnerModel partnerModel =
              Provider.of<PartnerModel>(navKey.currentContext!, listen: false);
          {
            print(
                'A new onMessageOpenedApp event was published! Data: ${partnerModel.data?.username}');
            if ((data ?? '').isNotEmpty) {
              showDialog(
                  context: navKey.currentContext!,
                  builder: (BuildContext context) {
                    return AlertDialog(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(15))),
                      contentPadding: EdgeInsets.only(top: 12.0, bottom: 10.0),
                      backgroundColor: Colors.white,
                      title: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "From ${partnerModel.data?.username}",
                                style: TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 18),
                              ),
                              GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: Icon(
                                    Icons.cancel,
                                    color: Colors.grey,
                                  )),
                            ],
                          ),
                        ],
                      ),
                      content: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 30),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  data ?? 'test',
                                  style: TextStyle(
                                      color: Colors.grey,
                                      fontWeight: FontWeight.w500,
                                      fontSize: 17),
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                              ],
                            )),
                      ),
                    );
                  });
            } else if (messageBody.contains(
                'your request to partner with ${partnerModel.data?.email} was not accepted')) {
              print('------->notification get =====');
              PartnerModel partnerModel = Provider.of<PartnerModel>(
                  navKey.currentContext!,
                  listen: false);
              final service = locator<APIService>();

              try {
                await service.request(CancelInviteRequest());
                await partnerModel.setState(null);
                await partnerModel.setData(null);
              } catch (err) {
                print(err);
              }
            }
          }
        }
        _refresh();
      });

      if (Platform.isIOS) {
        FirebaseMessaging.instance.getInitialMessage().then((event) async {
          if (event != null) {
            String payload = event.data['payload'];
            String messageBody = event.notification?.body ?? "";

            Map<String, dynamic> payloadMap = jsonDecode(payload);
            if (payloadMap.containsKey('data')) {
              dynamic data = payloadMap['data']['message'];
              {
                PartnerModel partnerModel = Provider.of<PartnerModel>(
                    navKey.currentContext!,
                    listen: false);
                print(
                    'A new onMessageOpenedApp event was published! Data: ${partnerModel.data?.username}');
                if ((data ?? '').isNotEmpty) {
                  print(
                      'A new onMessageOpenedApp event was published! Data: ${partnerModel.data?.username}');

                  showDialog(
                      context: navKey.currentContext!,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(15))),
                          contentPadding:
                              EdgeInsets.only(top: 12.0, bottom: 10.0),
                          backgroundColor: Colors.white,
                          title: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "From ${partnerModel.data?.username}",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 18),
                                  ),
                                  GestureDetector(
                                      onTap: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: Icon(
                                        Icons.cancel,
                                        color: Colors.grey,
                                      )),
                                ],
                              ),
                            ],
                          ),
                          content: SizedBox(
                            width: MediaQuery.of(context).size.width,
                            child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 30),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      data ?? 'test',
                                      style: TextStyle(
                                          color: Colors.grey,
                                          fontWeight: FontWeight.w500,
                                          fontSize: 17),
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                  ],
                                )),
                          ),
                        );
                      });
                } else if (messageBody.contains(
                    'your request to partner with ${partnerModel.data?.email} was not accepted')) {
                  print('------->notification get =====');
                  PartnerModel partnerModel = Provider.of<PartnerModel>(
                      navKey.currentContext!,
                      listen: false);
                  final service = locator<APIService>();

                  try {
                    await service.request(CancelInviteRequest());
                    await partnerModel.setState(null);
                    await partnerModel.setData(null);
                  } catch (err) {
                    print(err);
                  }
                }
              }
            }
            _refresh();
          }
        });
      }
    });

    // in App show message

    // FirebaseMessaging.onBackgroundMessage((RemoteMessage message) async {
    //   print("FirebaseMessaging called ==> ${message.notification?.toMap()}");
    //   NotificationServices notificationServices = NotificationServices();
    //   await notificationServices.showNotification(id: 1, title: message.notification?.title ?? "", body: message.notification?.body ?? "");
    //   _refresh();
    // });

    // UserModel userModel = Provider.of<UserModel>(context, listen: false);
    userModel.addListener(_userModelListener);
    _userModelListener();

    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    print('----dispose----');
    _controller.dispose();

    // UserModel userModel = Provider.of<UserModel>(context, listen: false);
    userModel.removeListener(_userModelListener);

    _closeCable();
    _closePolling();

    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      _refresh();
      _closeCable();
      _closePolling();
      _token = null;
      _userModelListener();
    }

    if (state == AppLifecycleState.paused) {
      _closeCable();
      _closePolling();
      if ((prefs?.get('isApiCallPending') ?? false) == true) {
        APIService service = locator<APIService>();
        UserModel userModel = Provider.of<UserModel>(context, listen: false);
        SyncModel syncModel = Provider.of<SyncModel>(context, listen: false);
        if (syncModel.sentActive != null) {
          print('syncModel ${syncModel.sentActive?.isSync}');
          print('syncModel ${syncModel.sentActive?.id}');
          final SyncResponse startedResponse =
              await service.request(StartSyncRequest(syncModel.sentActive!.id));
          await prefs?.setBool('isApiCallPending', false);
          await syncModel.setSentActive(startedResponse, userModel.data.id);

          Analytics analytics = locator<Analytics>();
          analytics.syncInput(userModel.data.id, startedResponse.id);

          print(
              "isApiCallPending =====>1 ${prefs?.getBool('isApiCallPending')}");
        }
      }
    }
  }

  void _closeCable() {
    if (_cable != null) {
      try {
        _cable?.disconnect();
      } finally {
        _cable = null;
      }
    }
  }

  void _startPolling() {
    _closePolling();
    _timer = Timer.periodic(Duration(seconds: 6), (_) {
      print('Running periodic refresh timer...');
      _refresh();
    });
  }

  void _closePolling() {
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }
  }

  void _userModelListener() {
    print('Running periodic refresh timer...');
    // UserModel userModel = Provider.of<UserModel>(context, listen: false);
    final syncModel = Provider.of<SyncModel>(context, listen: false);

    if (_token != userModel.token) {
      _token = userModel.token;
      _closeCable();
      _closePolling();

      if (_token != null) {
        _setPush();
        _getPartner(true);
        _getSyncSent(true);
        fetchSyncData(true);

        final String currentToken = userModel.token ?? '';
        _cable = Cable.connect(
          APIService.baseSocket + '?token=$_token',
          onConnected: () {
            print('Connected');
          },
          onError: (err) {
            print('Socket error: $err');
            print('Falling back to polling');
            print('Sync refresh hint received.222');

            _startPolling();
          },
          onDone: () {
            UserModel userModel =
                Provider.of<UserModel>(context, listen: false);
            if (userModel.token != null && userModel.token == currentToken) {
              print('Socket closed unexpected, falling back to polling');
              print('Sync refresh hint received.222');

              _startPolling();
            } else {
              print('Socket closed.');
            }
          },
        );

        _cable!.subscribe(
          'RefreshChannel',
          onSubscribed: () {
            print('Subcribed to refresh hints.');
            //_getPartner(true);
          },
          onDisconnected: () {
            print('Disconnected from refresh hints.');
            _closeCable();
            print('Sync refresh hint received.222');

            _startPolling();
          },
          onMessage: (Map message) async {
            print("message =====> $message");
            print("message =====> ${syncModel.waitingSync}");

            if (message.containsKey('message')) {
              String data = message['message'];
              if (data != null) {
                switch (data) {
                  case 'refresh_user':
                    // unimplemented
                    print('User refresh hint received.');
                    break;
                  case 'refresh_partner':
                    print('Partner refresh hint received.');
                    _getPartner(false);
                    break;
                  case 'refresh_sync':
                    print('Sync refresh hint received.');
                    _getSyncSent(false);
                    fetchSyncData(false);
                    break;
                  case 'sync_time_expire':
                    print('sync_time_expire refersh----->');

                    //if(syncModel != null && syncModel.waitingSync) {
                    await resetUi();
                    //}
                    break;
                  default:
                    // unimplemented hints
                    break;
                }
              }
            }
          },
          channelParams: {},
        );
      }
    }
  }

  resetUi() async {
    print('ttttt');
    await prefs?.setBool('isApiCallPending', false);
    //_syncInteractLock = true;

    final service = locator<APIService>();

    final partnerModel = Provider.of<PartnerModel>(context, listen: false);
    final syncModel = Provider.of<SyncModel>(context, listen: false);

    try {
      //print("partner linked ===> ${partnerModel.isLinked}");
      if (!partnerModel.isLinked) throw ('Partner not linked');
      print("partner linked ===> ${partnerModel.isLinked}");
      print('reset connection');
      await service.request(DeleteSyncRequest(syncModel.sentActive!.id,
          waitTime: syncModel.sentActive?.minutes ?? '0'));

      if (syncModel.synced) {
        print("id============>");
        final SyncResponse response = await service.request(GetSyncRequest(
          syncModel.sentActive!.id,
        ));
        print("id============>${response.syncedId}");
        await service.request(DeleteSyncRequest(response.syncedId,
            waitTime: syncModel.sentActive?.minutes ?? '0'));
      }
      // print('------>${syncModel.sentActive?.isSync}');
      // print('------>${syncModel.sentActive?.minutes}');
      // print('------>${syncModel.sentActive?.status}');
      // print('------>${syncModel.sentActive?.started}');
      // print('------>${syncModel.sentActive?.expiresAt}');
      // print('------>${syncModel.waitingSync}');
      await syncModel.setSentActive(null, -1);
    } catch (err) {
      print(err);
    } finally {
      //   _syncInteractLock = false;
    }
  }

  void _refresh() {
    if (mounted) {
      _getPartner(false);
      _getSyncSent(false);
      fetchSyncData(false);
    }
  }

  /// Information derived from `android`-`androidId` or `ios`-`identifierForVendor`
  static Future<String?> get getDeviceId async {
    String? deviceId;
    try {
      final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        // AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
        deviceId = await AndroidId().getId();
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
        deviceId = iosInfo.identifierForVendor;
      }
    } on PlatformException {
      deviceId = '';
    }
    return deviceId;
  }

  void _setPush() async {
    print('Setting push token');

    APIService service = locator<APIService>();
    // final id = await DeviceId.getID;
    final id = await getDeviceId;

    if (Platform.isIOS) {
      // fcm.onIosSettingsRegistered.listen((_) async {
      try {
        final token = await FirebaseMessaging.instance.getToken();
        print("IOS Token =======> $_token");
        await service.request(RegisterDeviceRequest(id!, token!));
      } catch (err) {
        print("error on IOS token ===> ${err.toString()}");
      }
      // });

      FirebaseMessaging.instance.requestPermission();
    } else {
      try {
        final token = await FirebaseMessaging.instance.getToken();
        print("Android Token =======> $_token");
        await service.request(RegisterDeviceRequest(id!, token!));
      } catch (err) {
        print("error on Android token ===> ${err.toString()}");
      }
    }
  }

  Future<void> fetchSyncData(bool initialRequest) async {
    APIService service = locator<APIService>();
    UserModel userModel = Provider.of<UserModel>(context, listen: false);

    try {
      //final UserResponse response1 = await service.request(SyncScoreRequest());
      final UserResponse userResponse =
          await service.request(SubscriptionDetailRequest());
      await userModel.setData(userResponse);
      final UserResponse userResponse1 =
          await service.request(SyncScoreRequest());
      await userModel.setData(
        userResponse1,
      );

      // If partner linked, stop here and don't do other requests
      return;
    } catch (err) {}
  }

  void _getPartner(bool initialRequest) async {
    //print('Fetching partner data');

    APIService service = locator<APIService>();
    UserModel userModel = Provider.of<UserModel>(context, listen: false);
    PartnerModel partnerModel =
        Provider.of<PartnerModel>(context, listen: false);

    try {
      final UserResponse response1 = await service.request(PartnerRequest());
      if (partnerModel.state == PartnerModelState.invited) {
        Analytics analytics = locator<Analytics>();
        analytics.partnerConnected(userModel.data.id, response1.id, false);
      }

      await partnerModel.setState(PartnerModelState.linked);
      await partnerModel.setData(response1);

      // If partner linked, stop here and don't do other requests
      return;
    } catch (err) {
      if (partnerModel.state == PartnerModelState.linked &&
          err == 'Partner not found') {
        await partnerModel.setState(null);
        await partnerModel.setData(null);
        print("error ==>");
        SyncModel syncModel = Provider.of<SyncModel>(context, listen: false);
        await syncModel.setSentActive(null, -1);
      }
    }

    try {
      print("partnerModel.state =======> ${partnerModel.state}");
      if (partnerModel.state != PartnerModelState.invitation) {
        final UserResponse response2 =
            await service.request(SentInviteRequest());
        print("response2 $response2");
        await partnerModel.setState(PartnerModelState.invited);
        // await partnerModel.setData(response2);
        await partnerModel.setData(response2, isSentInvite: _isSentInvite);
        print("response2 $_isSentInvite");
      } else {
        // print("response2 =======>");
        // final UserResponse response2 = await service.request(SentInviteRequest());
        // print("response2 $response2");
        // await partnerModel.setState(PartnerModelState.invited);
        // await partnerModel.setData(response2);
      }
    } catch (err) {
      if (partnerModel.state == PartnerModelState.invited &&
          err == 'No invitation found') {
        await partnerModel.setState(null);
        await partnerModel.setData(null);
        SyncModel syncModel = Provider.of<SyncModel>(context, listen: false);
        await syncModel.setSentActive(null, -1);
      }
    }

    try {
      if (partnerModel.state != PartnerModelState.invited) {
        final UserResponse response3 =
            await service.request(ReceivedInviteRequest());
        print("response3 $response3");
        await partnerModel.setState(PartnerModelState.invitation);
        await partnerModel.setData(response3);
      } else {
        final UserResponse response3 =
            await service.request(ReceivedInviteRequest());
        print("response3 ====> $response3");
        await partnerModel.setState(PartnerModelState.twoInvited);
        await partnerModel.setData(response3);
      }
    } catch (err) {
      print("Accept  ");
      if (partnerModel.state == PartnerModelState.invitation &&
          err == 'No invitation found') {
        await partnerModel.setState(null);
        await partnerModel.setData(null);
        SyncModel syncModel = Provider.of<SyncModel>(context, listen: false);
        await syncModel.setSentActive(null, -1);
      }
    }
  }

  void _getSyncSent(bool initialRequest) async {
    print('Fetching sync data');

    APIService service = locator<APIService>();
    UserModel userModel = Provider.of<UserModel>(context, listen: false);
    SyncModel syncModel = Provider.of<SyncModel>(context, listen: false);

    bool syncExists = false;
    if (syncModel.exists) {
      syncExists = true;
      print("isApiCallPending =====> ${prefs?.getBool('isApiCallPending')}");
      if ((prefs?.get('isApiCallPending') ?? false) == true) {
        // final SyncResponse startedResponse = await service.request(StartSyncRequest(syncModel.sentActive!.id));
        // await prefs?.setBool('isApiCallPending', false);
        // await syncModel.setSentActive(startedResponse, userModel.data.id);
        //
        // Analytics analytics = locator<Analytics>();
        // analytics.syncInput(userModel.data.id, startedResponse.id);
        await prefs?.setBool('isApiCallPending', false);

        final SyncResponse response = await service.request(SyncSentRequest());
        await syncModel.setSentActive(response, userModel.data.id);
        log("reopen check ====> ");
        if (response.isSync ||
            (response.expiresAt.isAfter(DateTime.now()) &&
                response.status != 'expired')) {
          if (!response.started && initialRequest) {
            log("reopen check ====> ");
            final SyncResponse startedResponse =
                await service.request(StartSyncRequest(response.id));
            await syncModel.setSentActive(startedResponse, userModel.data.id);

            Analytics analytics = locator<Analytics>();
            analytics.syncInput(userModel.data.id, startedResponse.id);
          } else {
            log("reopen check1 ====> ");
            await syncModel.setSentActive(response, userModel.data.id);
          }
        } else {
          syncExists = false;
          log("reopen check2 ====> ");
          await syncModel.setSentActive(null, -1);
        }
        print("sent active  =====>1 ${syncModel.sentActive?.isSync}");
      }

      if (!(syncModel.sentActive!.isSync)) {
        print("sent ==> ");
        final SyncResponse response =
            await service.request(GetSyncRequest(syncModel.sentActive!.id));
        await syncModel.setSentActive(response, userModel.data.id);
      }
      if ((syncModel.sentActive!.isSync)) {
        try {
          print("sent ==> isSync");
          final SyncResponse response =
              await service.request(GetSyncRequest(syncModel.sentActive!.id));

          if (response.isSync ||
              (response.expiresAt.isAfter(DateTime.now()) &&
                  response.status != 'expired')) {
            if (!response.started && initialRequest) {
              log("reopen check ====> ");
              final SyncResponse startedResponse =
                  await service.request(StartSyncRequest(response.id));
              await syncModel.setSentActive(startedResponse, userModel.data.id);

              Analytics analytics = locator<Analytics>();
              analytics.syncInput(userModel.data.id, startedResponse.id);
            } else {
              await syncModel.setSentActive(response, userModel.data.id);
            }
          } else {
            print("pppp Data not found");
            syncExists = false;
            await syncModel.setSentActive(null, -1);
            ButtonScreen.idCounts = {};
          }
        } catch (err) {
          print("Data not found");
          print(err);
        }
      }
      if (syncModel.sentActive != null && syncModel.sentActive!.isSync) {
        // final appModel = Provider.of<AppModel>(context, listen: false);
        if (syncModel.sentActive!.expiresAt.isBefore(DateTime.now())) {
          await syncModel.setSentActive(null, -1);
        } else if (syncModel.sentActive?.syncedId != null &&
            syncModel.sentActive!.syncedId > 0) {
          try {
            print('sync sucessfully -----');
            final SyncResponse response = await service
                .request(GetSyncRequest(syncModel.sentActive!.syncedId));
            // await prefs?.setBool('isApiCallPending', false);
            if (response.expiresAt.isBefore(DateTime.now())) {
              await syncModel.setSentActive(null, -1);
            }
          } catch (err) {
            print(err);
          }
        }
      }
    }

    if (!syncExists) {
      try {
        print("sync is  found");
        final SyncResponse response = await service.request(SyncSentRequest());
        await syncModel.setSentActive(response, userModel.data.id);
      } catch (err) {
        log("sync not found");
        if (initialRequest && err == 'Sync not found') {
          await syncModel.setSentActive(null, -1);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return UpgradeAlert(
      upgrader: Upgrader(
        // showIgnore: true,
        // showLater: false,
        durationUntilAlertAgain: const Duration(milliseconds: 500),
        // showReleaseNotes: false,
      ),
      child: Scaffold(
        body: SafeArea(
          child: _getContent(),
        ),
      ),
    );
  }

  Widget _getContent() {
    return Stack(
      children: <Widget>[
        PageView(
          controller: _controller,
          physics: NeverScrollableScrollPhysics(),
          children: <Widget>[
            PartnerScreen(),
            ButtonScreen(),
            SettingsScreen(
              onSignOut: () async {
                APIService service = locator<APIService>();

                try {
                  final partner = await service.request(SignOutRequest());
                  print("response ===> ${partner.toString()}");

                  if (partner.email == null || partner.email.isEmpty) {
                    throw ("Partner not found");
                  }
                } catch (err) {
                  print(("Error ====> $err"));
                }

                final appModel = Provider.of<AppModel>(context, listen: false);
                await appModel.setHasAskedRate(false);

                final syncModel =
                    Provider.of<SyncModel>(context, listen: false);
                await syncModel.setSentActive(null, -1);
                await syncModel.setSyncsSinceAskedRate(0);
                await syncModel.setLastSyncId(0);

                final userModel =
                    Provider.of<UserModel>(context, listen: false);
                await userModel.setToken(null);
                await userModel.setData(null);

                final partnerModel =
                    Provider.of<PartnerModel>(context, listen: false);
                await partnerModel.setState(null);
                await partnerModel.setData(null);
                ButtonScreen.idCounts = {};
                //await prefs?.remove('isApiCallPending');

                setState(() {
                  _page = 1;
                });
                _controller.jumpToPage(1);
              },
              onDeleteAccount: () async {
                print("cancel1 =====>");
                CommonDialog.commonAlertShowDialog(
                  context: context,
                  fontSize: 15,
                  height: 90,
                  width: 90,
                  dialogText:
                      "Are you sure you want to permanently remove your account and all associated data?",
                  imagePath: 'assets/Delete account Popup.png',
                  titleText: 'Delete Account?',
                  multipleButton: true,
                  onNo: () {
                    setState(() {
                      isSelected = false;
                    });
                    Navigator.pop(context);
                  },
                  onYes: () async {
                    setState(() {
                      isSelected = true;
                    });
                    APIService service = locator<APIService>();
                    try {
                      final partner =
                          await service.request(DeleteAccountRequest());
                      print("response ===> ${partner.toString()}");

                      if (partner.email == null || partner.email.isEmpty) {
                        throw ("Partner not found");
                      }
                    } catch (err) {
                      print(("Error ====> $err"));
                    }

                    final appModel =
                        Provider.of<AppModel>(context, listen: false);
                    await appModel.setHasAskedRate(false);

                    final syncModel =
                        Provider.of<SyncModel>(context, listen: false);
                    await syncModel.setSentActive(null, -1);
                    await syncModel.setSyncsSinceAskedRate(0);
                    await syncModel.setLastSyncId(0);

                    final userModel =
                        Provider.of<UserModel>(context, listen: false);
                    await userModel.setToken(null);
                    await userModel.setData(null);

                    final partnerModel =
                        Provider.of<PartnerModel>(context, listen: false);
                    await partnerModel.setState(null);
                    await partnerModel.setData(null);
                    //await prefs?.remove('isApiCallPending');
                    Navigator.of(context).pop();
                    setState(() {
                      _page = 1;
                    });
                    _controller.jumpToPage(1);
                  },
                );
              },
              onUnlink: () async {
                final result = await showDialog(
                  builder: (context) => AlertBox(
                    title: "Unlink Partner",
                    subtitle: "Do you want to unlink your partner?",
                    type: AlertBox.YesNoActions,
                  ),
                  barrierDismissible: false,
                  context: context,
                );

                if (result) {
                  final partnerModel =
                      Provider.of<PartnerModel>(context, listen: false);
                  final userModel =
                      Provider.of<UserModel>(context, listen: false);
                  // final syncModel =
                  // Provider.of<SyncModel>(context, listen: false);

                  int? userId = userModel.data.id;
                  int? partnerId = partnerModel.data?.id;

                  APIService service = locator<APIService>();
                  await service.request(
                      UnlinkPartnerRequest(partnerModel.data?.id ?? 0));
                  //await service.request(UnlinkPartnerRequest(userModel.data?.id ?? 0));
                  // await service.request(DeleteSyncRequest());
                  await partnerModel.setState(null);
                  await partnerModel.setData(null);

                  final syncModel =
                      Provider.of<SyncModel>(context, listen: false);
                  await syncModel.setSentActive(null, -1);

                  Analytics analytics = locator<Analytics>();
                  analytics.unpartner(userId, partnerId!);
                }
              },
            ),
          ],
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Toolbar(
            page: _page,
            onPageChange: (page) {
              setState(() {
                _page = page;
              });
              _controller.jumpToPage(page);
            },
          ),
        ),
      ],
    );
  }
}
