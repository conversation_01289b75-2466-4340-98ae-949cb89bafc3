import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/router.dart';
import 'package:lovesync/ui/widget/button.dart';

class RequestSent extends StatefulWidget {
  RequestSent({Key? key}) : super(key: key);

  @override
  RequestSentState createState() => RequestSentState();
}

class RequestSentState extends State<RequestSent> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: colorBackground,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Container(
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                colors: [colorPrimary, colorAccent],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              )),
              child: Safe<PERSON><PERSON>(
                  child: Container(
                      margin: EdgeInsets.only(top: 80),
                      child: Stack(
                        children: <Widget>[
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Container(
                                margin: EdgeInsets.only(left: 48),
                                child: Text("Invite Sent!", style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold)),
                              ),
                            ],
                          ),
                          Lottie.asset('assets/lottie/hearts.json'),
                        ],
                      ))),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(48),
                children: <Widget>[
                  SizedBox(height: 24),
                  Text("We'll email you when they accept!",
                      style: TextStyle(
                        fontSize: 16,
                      )),
                  SizedBox(height: 48),
                  Button(
                    "Back to LoveSync",
                    theme: Button.THEME_DEFAULT,
                    onTap: () {
                      Navigator.pushReplacementNamed(context, RouteName.Home);
                    },
                  ),
                ],
              ),
            )
          ],
        ));
  }
}
