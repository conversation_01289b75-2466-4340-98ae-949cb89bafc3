import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lovesync/analytics.dart';
import 'package:lovesync/api/requests/login.dart';
import 'package:lovesync/api/requests/partner.dart';
import 'package:lovesync/api/requests/received_invite.dart';
import 'package:lovesync/api/requests/resend_confirmation.dart';
import 'package:lovesync/api/responses/login.dart';
import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/data/model/partner.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/router.dart';
import 'package:lovesync/ui/dialog.dart';
import 'package:lovesync/ui/widget/button.dart';
import 'package:provider/provider.dart';

class LoginView extends StatefulWidget {
  LoginView({Key? key}) : super(key: key);

  @override
  LoginViewState createState() => LoginViewState();
}

class LoginViewState extends State<LoginView> {
  String _errorMessage = "";

  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  Future<bool> _checkPartner() async {
    APIService service = locator<APIService>();

    try {
      final UserResponse partner = await service.request(PartnerRequest());

      if (partner.email == null) {
        throw ("Partner not found");
      }
    } catch (err) {
      return false;
    }

    return true;
  }

  Future<bool> _checkInvite() async {
    APIService service = locator<APIService>();

    try {
      final UserResponse partner = await service.request(ReceivedInviteRequest());

      if (partner.email == null) {
        throw ("Partner not found");
      }
    } catch (err) {
      return false;
    }

    return true;
  }

  void _onSubmit(UserModel userModel) async {
    HapticFeedback.lightImpact();

    String email = emailController.text.trim();
    String password = passwordController.text.trim();

    setState(() {
      _errorMessage = "";
    });

    APIService service = locator<APIService>();

    try {
      final LoginResponse response =
          await service.request(LoginRequest(email, password));

      await userModel.setToken(response.token);
      await userModel.setData(response.user);

      if (response.user != null) {
        print("analytics called");
        Analytics analytics = locator<Analytics>();
        analytics.login(response.user!.id);
      }

      FocusManager.instance.primaryFocus?.unfocus();
      PartnerModel partnerModel = Provider.of<PartnerModel>(context, listen: false);
      //PartnerModel partnerModel;
      log("partner ====> ${partnerModel.isRequesting}");
      log("partner ====> ${partnerModel.isLinked}");
      log("partner ====> ${partnerModel.isInvited}");
      if (await _checkPartner() || await _checkInvite() || partnerModel.isInvited) {
        Navigator.pushReplacementNamed(context, RouteName.Home);
      } else {
        Navigator.pushReplacementNamed(context, RouteName.LinkPartner);
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        print("error ======> $_errorMessage");
      });

      if (e == 'User not found') {
        //print("error ======> $_errorMessage");
        final result = await FlutterNativeDialog.showConfirmDialog(
          title: 'User not found',
          message: 'Would you like to signup?',
          positiveButtonText: 'Sign up',
          negativeButtonText: 'Cancel',
        );
        print("error1 ======> $result");
        if (result) {
          print("error2 ======> $result");
          Analytics analytics = locator<Analytics>();
          analytics.signupOpened();
          Navigator.pushNamed(context, RouteName.Register,
              arguments: <String, dynamic>{
                "email": email,
              }).then((result) {
            if (result != null) {
              if (result is Map<String, dynamic>) {
                if (result.containsKey('email')) {
                  emailController.text = result['email'];
                }
              }
            }
          });
          passwordController.text = '';

          setState(() {
            _errorMessage = '';
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    UserModel userModel = Provider.of<UserModel>(context, listen: false);
    return Scaffold(
        backgroundColor: colorBackground,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: SafeArea(
                  child: Container(
                margin:
                    EdgeInsets.only(top: 20, left: 48, right: 12, bottom: 36),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 5.0),
                          child: Image.asset("assets/brand/wordmark.png"),
                        ),
                        Spacer(),
                        IconButton(
                          icon: Icon(Icons.close),
                          onPressed: () {
                            Navigator.pop(context);
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text("Never miss a chance for romance!",
                        style: TextStyle(fontSize: 16, color: Colors.black45)),
                  ],
                ),
              )),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(48),
                children: <Widget>[
                  TextField(
                    controller: emailController,
                    decoration: InputDecoration(
                      labelText: 'Email',
                      labelStyle: TextStyle(
                        color: Color(0xFFE5EAED),
                      ),
                      enabledBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFF5B646C))),
                      focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: colorPrimary)),
                    ),
                  ),
                  SizedBox(height: 12),
                  TextField(
                    controller: passwordController,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: 'Password',
                      labelStyle: TextStyle(
                        color: Color(0xFFE5EAED),
                      ),
                      enabledBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFF5B646C))),
                      focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: colorPrimary)),
                    ),
                  ),
                  Visibility(
                    visible: _errorMessage.isNotEmpty,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(height: 24),
                        Text(
                          _errorMessage,
                          style: TextStyle(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: _errorMessage.contains("not confirmed"),
                    child: Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: Button(
                        "Resend activation e-mail?",
                        theme: Button.THEME_POSITIVE,
                        onTap: () async {
                          APIService service = locator<APIService>();

                          try {
                            final String message = await service.request(
                                ResendConfirmationRequest(
                                    emailController.text));

                            setState(() {
                              _errorMessage = message;
                            });
                          } catch (e) {
                            setState(() {
                              _errorMessage = e.toString();
                            });
                          }
                        },
                      ),
                    ),
                  ),
                  SizedBox(height: 24),
                  Align(
                    alignment: Alignment.center,
                    child: Stack(
                      children: <Widget>[
                        Container(
                          width: 100,
                          height: 100,
                          padding: EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                                colors: [colorPrimary, colorAccent],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter),
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                                color: colorBackground,
                                borderRadius: BorderRadius.circular(46)),
                          ),
                        ),
                        Material(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(50),
                          child: InkWell(
                            onTap: () {
                              if(emailController.text.isNotEmpty && passwordController.text.isNotEmpty) {
                                _onSubmit(userModel);
                              }else{
                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("Enter Email and Password")));
                              }
                            },
                            splashColor: colorAccent.withAlpha(50),
                            focusColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            borderRadius: BorderRadius.circular(50),
                            child: Container(
                              width: 100,
                              height: 100,
                              child: Align(
                                alignment: Alignment.center,
                                child: Text("Log In",
                                    style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.bold,
                                        color: colorAccent)),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 12),
                  Button(
                    "",
                    child: RichText(
                      text: TextSpan(
                        style: TextStyle(color: Colors.white, fontSize: 15.0),
                        children: [
                          TextSpan(text: "I'm a new user. "),
                          TextSpan(
                              text: "Sign Up",
                              style: TextStyle(color: colorAccent)),
                        ],
                      ),
                    ),
                    onTap: () async {
                      Analytics analytics = locator<Analytics>();
                      analytics.signupOpened();

                      Navigator.pushNamed(context, RouteName.Register)
                          .then((result) {
                        if (result != null) {
                          if (result is Map<String, dynamic>) {
                            if (result.containsKey('email')) {
                              emailController.text = result['email'];
                            }
                          } else {
                            setState(() {
                              _errorMessage = result as String;
                            });
                          }
                        }
                      });
                      passwordController.text = '';
                      setState(() {
                        _errorMessage = '';
                      });
                    },
                  ),
                  SizedBox(height: 12),
                  Button(
                    "Forgot your password?",
                    onTap: () {
                      Navigator.pushNamed(context, RouteName.ResetPassword);
                      passwordController.text = '';
                    },
                  ),
                ],
              ),
            )
          ],
        ));
  }
}
