import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lovesync/api/requests/register.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/ui/widget/alert.dart';
import 'package:url_launcher/url_launcher.dart';

class RegisterView extends StatefulWidget {
  final String email;

  RegisterView({Key? key, required this.email}) : super(key: key);

  @override
  RegisterViewState createState() => RegisterViewState();
}

class RegisterViewState extends State<RegisterView> {
  String _errorMessage = "";

  final usernameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    emailController.text = widget.email;
  }

  void _onSubmit() async {
    HapticFeedback.lightImpact();

    String username = usernameController.text;
    String email = emailController.text;
    String password = passwordController.text;

    setState(() {
      _errorMessage = "";
    });

    APIService service = locator<APIService>();

    try {
      await service.request(RegisterRequest(email, username, password));

      await showDialog(
        builder: (context) => AlertBox(
          title: "Register",
          subtitle: "Please check your email for a confirmation link",
        ),
        context: context,
      );

      var result = Map<String, dynamic>();
      result['email'] = emailController.text;
      Navigator.pop(context, result);
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: colorBackground,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: SafeArea(
                  child: Container(
                margin: EdgeInsets.only(top: 20, left: 48, right: 12, bottom: 36),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 5.0),
                          child: Image.asset("assets/brand/wordmark.png"),
                        ),
                        Spacer(),
                        IconButton(
                          icon: Icon(Icons.close),
                          onPressed: () {
                            Navigator.pop(context);
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text("Never miss a chance for romance!", style: TextStyle(fontSize: 16, color: Colors.black45)),
                  ],
                ),
              )),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(48),
                children: <Widget>[
                  TextField(
                    controller: usernameController,
                    decoration: InputDecoration(
                      labelText: 'Display Name',
                      labelStyle: TextStyle(
                        color: Color(0xFFE5EAED),
                      ),
                      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Color(0xFF5B646C))),
                      focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: colorPrimary)),
                    ),
                  ),
                  SizedBox(height: 10.0),
                  TextField(
                    controller: emailController,
                    decoration: InputDecoration(
                      labelText: 'Email',
                      labelStyle: TextStyle(
                        color: Color(0xFFE5EAED),
                      ),
                      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Color(0xFF5B646C))),
                      focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: colorPrimary)),
                    ),
                  ),
                  SizedBox(height: 10.0),
                  TextField(
                    controller: passwordController,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: 'Password',
                      labelStyle: TextStyle(
                        color: Color(0xFFE5EAED),
                      ),
                      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Color(0xFF5B646C))),
                      focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: colorPrimary)),
                    ),
                  ),
                  Visibility(
                    visible: _errorMessage.isNotEmpty,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(height: 24),
                        Text(
                          _errorMessage,
                          style: TextStyle(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24),
                  Align(
                    alignment: Alignment.center,
                    child: Stack(
                      children: <Widget>[
                        Container(
                          width: 100,
                          height: 100,
                          padding: EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(colors: [colorPrimary, colorAccent], begin: Alignment.topCenter, end: Alignment.bottomCenter),
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Container(
                            decoration: BoxDecoration(color: colorBackground, borderRadius: BorderRadius.circular(46)),
                          ),
                        ),
                        Material(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(50),
                          child: InkWell(
                            onTap: () {
                              _onSubmit();
                            },
                            splashColor: colorAccent.withAlpha(50),
                            focusColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            borderRadius: BorderRadius.circular(50),
                            child: Container(
                              width: 100,
                              height: 100,
                              child: Align(
                                alignment: Alignment.center,
                                child: Text('Sign Up', style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold, color: colorAccent)),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24.0),
                  RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                        style: TextStyle(
                          height: 1.5,
                        ),
                        children: [
                          TextSpan(text: 'You agree to our '),
                          TextSpan(
                            style: TextStyle(fontWeight: FontWeight.bold),
                            text: 'terms',
                            recognizer: TapGestureRecognizer()
                              ..onTap = () async {
                                await launchUrl(Uri.parse('https://www.freeprivacypolicy.com/terms/view/57d4853aac851b2baba5c486557fc8ae'), mode: LaunchMode.inAppWebView);
                              },
                          ),
                          TextSpan(text: ' and '),
                          TextSpan(
                            style: TextStyle(fontWeight: FontWeight.bold),
                            text: 'privacy',
                            recognizer: TapGestureRecognizer()
                              ..onTap = () async {
                                await launchUrl(Uri.parse('https://www.freeprivacypolicy.com/privacy/view/6672ab5134223f021414a55da8131c13'), mode: LaunchMode.inAppWebView);
                              },
                          ),
                          TextSpan(text: ' policies by creating an account'),
                        ]),
                  ),
                ],
              ),
            )
          ],
        ));
  }
}
