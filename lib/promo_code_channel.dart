// import 'package:flutter/services.dart';
//
// class PromoCodeChannel {
//   static const MethodChannel _channel = MethodChannel('promo_code_channel');
//
//   static Future<void> presentCodeRedemptionSheet() async {
//     try {
//       await _channel.invokeMethod('presentCodeRedemptionSheet');
//     } on PlatformException catch (e) {
//       throw 'Error presenting code redemption sheet: ${e.message}';
//     }
//   }
// }
