class Config {
  static const int _API_LOCAL = 0;
  static const int _API_DEVELOP = 1;
  static const int _API_PRODUCTION = 2;

  // Modify API Environment:
  static int _api = _API_PRODUCTION;
  static String apiUrl1 = 'http://192.168.0.4:3001/v1';
  static get apiUrl {
    switch (_api) {
      case _API_LOCAL:
        return 'http://10.0.2.2:3000/v1';
      case _API_DEVELOP:
        return 'https://api.develop.lovesync.com/v1';
      case _API_PRODUCTION:
          return 'https://api.lovesync.com/v1';
       // return apiUrl1;
      //return 'https://$_API_DOMAIN.ngrok.io/v1';
    }
  }

  static get apiSocket {
    switch (_api) {
      case _API_LOCAL:
        return 'ws://10.0.2.2:3000/cable';
      case _API_DEVELOP:
        return 'wss://api.develop.lovesync.com/cable';
      case _API_PRODUCTION:
         return 'wss://api.lovesync.com/cable';
        // return 'ws://52.196.178.187/cable';
      //  return 'ws://192.168.0.122:3005/cable';
    }
  }
}
