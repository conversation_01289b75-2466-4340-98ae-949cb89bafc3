import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

class Analytics {
  late FirebaseAnalytics _firebase;
  late FacebookAppEvents _facebook;

  Analytics() {
    _firebase = FirebaseAnalytics.instance;
    _facebook = FacebookAppEvents();
    print('analytics ${_firebase != null} ${_facebook != null}');
  }

  void setUser(int id, String? email) {
    if (id >= 0) {
      _facebook.setUserID(id.toString());
    } else {
      _facebook.setUserID('');
    }

    _facebook.setUserData(
      email: email,
    );
  }

  void onboardingDone() {
    _firebase.logEvent(name: 'onboarding_done');
    _facebook.logEvent(name: 'onboarding_done');
  }

  void signupOpened() {
    _firebase.logEvent(name: 'signup_opened');
    _facebook.logEvent(name: 'signup_opened');
  }

  void login(int userId) {
    _firebase.logEvent(
      name: 'login_performed',
      parameters: {
        'user_id': userId,
      },
    );

    _facebook.logEvent(
      name: 'login_performed',
      parameters: <String, dynamic>{
        'user_id': userId,
      },
    );
  }

  void partnerInvited(int userId, String email) {
    _firebase.logEvent(
      name: 'partner_invited',
      parameters: {
        'user_id': userId,
        'invited_email': email,
      },
    );

    _facebook.logEvent(
      name: 'partner_invited',
      parameters: <String, dynamic>{
        'user_id': userId,
        'invited_email': email,
      },
    );
  }

  void partnerConnected(int userId, int? partnerId, bool acceptPerformed) {
    _firebase.logEvent(
      name: 'partner_connected',
      parameters: {
        'user_id': userId,
        'partner_id': partnerId ?? -1,
        'accept_performed': acceptPerformed.toString(),
      },
    );

    _facebook.logEvent(
      name: 'partner_connected',
      parameters: <String, dynamic>{
        'user_id': userId,
        'partner_id': partnerId,
        'accept_performed': acceptPerformed,
      },
    );
  }

  void syncInput(int userId, int syncId) {
    _firebase.logEvent(
      name: 'sync_input',
      parameters: {
        'user_id': userId,
        'sync_id': syncId,
        'activated_on_client': true.toString(),
      },
    );

    _facebook.logEvent(
      name: 'sync_input',
      parameters: <String, dynamic>{
        'user_id': userId,
        'sync_id': syncId,
        'activated_on_client': true,
      },
    );
  }

  void syncConfirmed(int userId, int syncId) {
    _firebase.logEvent(
      name: 'sync_confirm',
      parameters: {
        'user_id': userId,
        'sync_id': syncId,
      },
    );

    _facebook.logEvent(
      name: 'sync_confirm',
      parameters: <String, dynamic>{
        'user_id': userId,
        'sync_id': syncId,
      },
    );
  }

  void unpartner(int userId, int partnerId) {
    _firebase.logEvent(
      name: 'unpartner_performed',
      parameters: {
        'user_id': userId,
        'partner_id': partnerId,
      },
    );

    _facebook.logEvent(
      name: 'unpartner_performed',
      parameters: <String, dynamic>{
        'user_id': userId,
        'partner_id': partnerId,
      },
    );
  }
}
