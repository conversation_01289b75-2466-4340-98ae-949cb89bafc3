import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class Avatar extends StatelessWidget {
  final String image;
  final double size;

  Avatar({required this.image, required this.size});

  @override
  Widget build(BuildContext context) {
    if (image.isEmpty) {
     // print("image");
      return _getGeneric();
    } else
      if (image.startsWith('https')) {
      print("image1");
      return CachedNetworkImage(
       width: size,
        height: size,
        imageUrl: image,
        imageBuilder: (context, imageProvider) => AnimatedContainer(
          duration: Duration(milliseconds: 400),
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            image: DecorationImage(
              image: imageProvider,
              fit: BoxFit.cover,
            ),
          ),
        ),

        placeholder: (context, url) => _getGeneric(),
        errorWidget: (BuildContext context, String url, dynamic error) => _getGeneric(),
      );
    } else {
      try {
        print("error£££");
        var file = File(image);
        if (file.existsSync()) {
          return AnimatedContainer(

            duration: Duration(milliseconds: 400),
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image: FileImage(file),
                fit: BoxFit.cover,
              ),
            ),
          );
        }
      } catch (_) {
        // ignore, go to generic
        // print("error£££-");
      }
      return _getGeneric();
    }
  }

  Widget _getGeneric() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 400),
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        // image: DecorationImage(
        //   image: AssetImage('assets/illustrations/generic.png'),
        //   fit: BoxFit.cover,
        // ),
      ),
      child: Image.asset("assets/illustrations/generic.png"),
    );
  }
}
