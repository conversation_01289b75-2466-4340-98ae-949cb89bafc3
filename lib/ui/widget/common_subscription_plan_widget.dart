import 'package:flutter/material.dart';
import 'package:lovesync/colors.dart';

import '../../purchase_setup/subscritpion_provider.dart';
class CustomUpgradedRadioButton extends StatelessWidget {
  final String months;
  final String dollar;
  final String yearlyOrMonthly;
  final String? percentage;
  final bool isOfferShow;
  final bool isFreeTrail;
  final int index;
  final UpdateList provider;
  final Function()? onTap;

  const CustomUpgradedRadioButton({
    Key? key,
    required this.months,
    required this.dollar,
    required this.yearlyOrMonthly,
    required this.isOfferShow,
    required this.isFreeTrail,
    required this.index,
    required this.provider,
    this.onTap,
    this.percentage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.black,
      //highlightColor: Colors.black,
      borderRadius:BorderRadius.all(Radius.circular(10)),
      onTap: onTap,
      child: Stack(children: [
        Container(
          decoration: BoxDecoration(
              border: Border.all(
                  color: provider.selectedBoolValue[index]
                      ? Color(0xFFFF7D80)
                      : Color(0xFF878E95),
                 ),
              borderRadius: BorderRadius.all(Radius.circular(10))),
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: 10,
                vertical: isFreeTrail
                    ? 15
                    :  25),
            child: Row(
              children: [
                SizedBox(
                  height: 28,
                  width: 28,
                  child: Radio<bool>(
                      fillColor: MaterialStateProperty.resolveWith(
                              (states) => Color(0xFFFF7D80)),
                      value: true,
                      groupValue: provider.selectedBoolValue[index],
                      onChanged: (bool? val) {
                        provider.updatedPlanList(index, val ?? true);
                        if (index == 0) {
                          provider.productData(provider.myProductList[1]);
                        } else if (index == 1) {
                          provider.productData(provider.myProductList[0]);
                        }
                      }),
                ),
                SizedBox(width:  14),
                isFreeTrail
                    ? Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                          'startAFreeTrial',
                          style: TextStyle(
                          overflow: TextOverflow.ellipsis, fontSize:  16, fontWeight: FontWeight.w700),),
                      
                      Padding(
                        padding: EdgeInsets.only(top: 4),
                        child: Row(
                          children: [
                            Flexible(
                              child: RichText(
                                text: TextSpan(
                                    text:
                                    'enableOneWeekTrial ',
                                    style: TextStyle(
                                        fontSize:  12,
                                        color: Color(0xFF878E95),
                                        fontWeight: FontWeight.w400),
                                    children: <TextSpan>[
                                      TextSpan(
                                          text: dollar,
                                          style: TextStyle(
                                              fontSize:
                                               12,

                                              color: Color(0xFF878E95),
                                              fontWeight:FontWeight.w700)),
                                      TextSpan(
                                          text: 'couple',
                                          style: TextStyle(
                                              fontSize:
                                               12,

                                              color: Color(0xFF878E95),
                                              fontWeight:
                                              FontWeight.w400)),
                                    ]),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
                    : Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                months,
                                style: TextStyle( fontSize:  20,
                                    fontWeight: FontWeight.w700),
                               ),
                            SizedBox(height: 5,),
                            (isOfferShow == false) ?
                            Text("FREE TRIAL: 1 week free and then $dollar per month",style: TextStyle(color: Colors.grey.shade500),) :  Container(),
                            // Text("Enable free trial 1 week free then $dollar per month",style: TextStyle(color: Colors.grey.shade500),) :  Container(),
                          ],
                        ),
                      ),

                    ],
                  ),
                ),
                isFreeTrail
                    ? Container()
                    : Expanded(
                  child: FittedBox(
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Row(
                          children: [
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 7),
                              child: Text(
                                  dollar,
                                  style: TextStyle( fontSize:  30,
                                      color: provider.selectedBoolValue[index]
                                          ? Color(0xFFFF7D80)
                                          : Colors.white,
                                      fontWeight: FontWeight.w700),
                                 ),
                            ),
                            Text(
                               yearlyOrMonthly,
                            style: TextStyle(
                                fontSize:  18,
                                fontWeight: FontWeight.w500
                            ),
                          ),
                          ],
                        ),
                        // Positioned( right: 0, bottom: 5,child: Text("Per Couple"))
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
        Positioned( right: 10, bottom: 5,child: Row(
          children: [
            Icon(Icons.star,color: allButtonColor,size: 10,),
            SizedBox(width: 5,),
            Text("Per Couple"),
          ],
        )),


        isOfferShow
            ? Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: EdgeInsets.symmetric(
                  horizontal: 11, vertical:  3),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topRight: Radius.circular(10),
                      bottomLeft: Radius.circular(10)),
                  color: Color(0xFFFF7D80)),
              child: Text(
                 percentage ?? '',
                  // text: translation(context).save,
                  style: TextStyle(
                      fontSize:  12,
                      color: Colors.white,
                      fontWeight: FontWeight.w500
                  ),
                 ),
            ))
            : Container()
      ]),
    );
  }
}