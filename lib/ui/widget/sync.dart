import 'dart:developer';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/data/model/partner.dart';
import 'package:lovesync/ui/widget/avatar.dart';
import 'package:provider/provider.dart';

enum SyncState {
  OFF,
  INTERACT,
  PENDING,
  ON,
}

class Sync extends StatefulWidget {
  final double size;
  final String time;
  final double fraction;
  final SyncState state;
  final Color glowColor;
  final Duration avatarDuration;
  final bool avatarVisible;
  final double? avatarSize;
  final double avatarDx;
  final double avatarDy;
  final VoidCallback onPush;
  final VoidCallback onRelease;

  Sync({
    Key? key,
    required this.size,
    required this.time,
    required this.fraction,
    required this.state,
    required this.glowColor,
    required this.avatarDuration,
    required this.avatarVisible,
    required this.avatarSize,
    required this.avatarDx,
    required this.avatarDy,
    required this.onPush,
    required this.onRelease,
  }) : super(key: key);

  @override
  _SyncState createState() => _SyncState();
}

class _SyncState extends State<Sync> {
  late AssetImage buttonBackground;

  @override
  void initState() {
    super.initState();
    buttonBackground = AssetImage('assets/decor/button_background.png');
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    precacheImage(buttonBackground, context);
  }

  @override
  Widget build(BuildContext context) {
    //print("sync screen called");
    return Container(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          // Gradient Shadows
          Container(
            width: widget.size * 0.75,
            height: widget.size * 0.75,
            child: Stack(
              children: <Widget>[
                Align(
                  alignment: Alignment.topLeft,
                  child: Container(
                    width: widget.size * 0.55,
                    height: widget.size * 0.55,
                    decoration: BoxDecoration(
                      gradient: RadialGradient(
                        colors: [Color(0x80222428), colorBackground],
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.bottomRight,
                  child: Opacity(
                    opacity: 0.75,
                    child: Container(
                      width: widget.size * 0.55,
                      height: widget.size * 0.55,
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          colors: [Colors.black, colorBackground],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Main circle
          GestureDetector(
            onPanDown: (DragDownDetails details) {
              log("press ==> onPanDown");
              widget.onPush();
            },
            onPanEnd: (DragEndDetails details) {
              log("press ==> onPanEnd");
              widget.onRelease();
            },
            onPanCancel: () {
              log("press ==> onPanCancel");
              widget.onRelease();
            },
            child: AnimatedContainer(
              duration: Duration(milliseconds: 400),
              width: widget.size * (widget.state == SyncState.INTERACT ? 0.7 : 0.64),
              height: widget.size * (widget.state == SyncState.INTERACT ? 0.7 : 0.64),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(image: buttonBackground),
                boxShadow: [
                  BoxShadow(
                    color: widget.glowColor,
                    blurRadius: 10.0,
                    spreadRadius: 4.0,
                  ),
                ],
              ),
            ),
          ),

          // Logomark
          IgnorePointer(
            child: Container(
              width: widget.size * 0.33,
              height: widget.size * 0.33,
              decoration: BoxDecoration(
                image: DecorationImage(image: AssetImage('assets/brand/buttonmark.png')),
              ),
            ),
          ),

          IgnorePointer(
            child: Visibility(
              visible: widget.avatarVisible,
              child: AnimatedContainer(
                curve: Curves.ease,
               alignment: AlignmentDirectional(widget.avatarDx, widget.avatarDy),
                duration: widget.avatarDuration,
                child: Selector<PartnerModel, String>(
                  selector: (context, partnerModel) {
                    if (partnerModel.isLinked) {
                      // print("partner image ===> ");
                      // print("partner avatarDx ===> ${widget.avatarDx}");
                      // print("partner avatarDy ===> ${widget.avatarDy}");
                      return partnerModel.data?.image ?? '';
                    } else {
                      print("Data not found");
                      return '';
                    }
                  },
                  builder: (context, image, __) {
                    // print("partner profile image ===> $image");
                    // print("avatarSize ===> ${widget.avatarSize}");
                    //print("size =====> ${widget.size}");
                    return Avatar(
                      image: image,
                      size: widget.avatarSize ?? widget.size * 0.64 - 48.0 + 10.0,
                    );
                  },
                ),
              ),
            ),
          ),

          // Gradient spinner
          IgnorePointer(
            child: AnimatedOpacity(
              opacity: widget.state == SyncState.OFF ? 0.0 : 1.0,
              duration: Duration(milliseconds: 300),
              child: Container(
                width: widget.size * 0.64,
                height: widget.size * 0.64,
                child: ShaderMask(
                  blendMode: BlendMode.srcATop,
                  shaderCallback: (rect) {
                    print('start anmation');
                    return SweepGradient(
                      transform: GradientRotation(math.pi * 2 * 0.73),
                      colors: [
                        Color(0xFFFE8385),
                        Color(0xFFFF8D74),
                        Color(0xFFFF9A65),
                        Color(0xFFF8A959),
                        Color(0xFFECB852),
                        Color(0xFFDBC752),
                        Color(0xFFC6D65B),
                        Color(0xFFABE36E),
                        Color(0xFF8AF087),
                        Color(0xFF5BFBA7),
                      ],
                    ).createShader(Rect.fromLTWH(0, 0, widget.size * 0.64, widget.size * 0.64));
                  },
                  child: Container(
                    child: CustomPaint(
                      painter: _RingPainter(fraction: widget.fraction),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Ticks
          IgnorePointer(
            child: Container(
              width: widget.size,
              height: widget.size,
              child: CustomPaint(
                painter: _TickPainter(),
              ),
            ),
          ),

          // Timer text
          IgnorePointer(
            child: Visibility(
              visible: widget.state != SyncState.OFF,
              child: Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()..rotateZ(widget.fraction * math.pi * 2.0),
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  child: Align(
                    alignment: Alignment.topCenter,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 5.0),
                      decoration: BoxDecoration(
                        color: colorBackground,
                      ),
                      child: Text(widget.time ?? ''),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _RingPainter extends CustomPainter {
  final double fraction;
  late Paint _paint;

  _RingPainter({required this.fraction}) {
    _paint = Paint();
    _paint.strokeWidth = 10.0;
    _paint.style = PaintingStyle.stroke;
    _paint.strokeCap = StrokeCap.round;
    _paint.color = Color(0xFF272A2F);
  }

  @override
  void paint(Canvas canvas, Size size) {
    var rect = Offset.zero & size;
    canvas.drawArc(rect.deflate(24.0), -math.pi / 2, (math.pi * 2) * fraction, false, _paint);
  }

  @override
  bool shouldRepaint(_RingPainter oldDelegate) {
    return oldDelegate.fraction != fraction;
  }
}

class _TickPainter extends CustomPainter {
  late Paint _tickPaint;

  _TickPainter() {
    _tickPaint = Paint();
    _tickPaint.strokeWidth = 1;
    _tickPaint.color = Color(0xFF272A2F);
  }

  @override
  void paint(Canvas canvas, Size size) {
    var rect = Offset.zero & size;
    var radius = rect.width / 2;
    var lineWidth = 10.0;

    for (var a = 0; a < 360; a += 6) {
      var radians = a * (math.pi / 180.0);
      var x1 = radius * math.sin(radians) + radius;
      var x2 = (radius - lineWidth) * math.sin(radians) + radius;
      var y1 = radius * math.cos(radians) + radius;
      var y2 = (radius - lineWidth) * math.cos(radians) + radius;

      var p1 = Offset(x1, y1);
      var p2 = Offset(x2, y2);
      canvas.drawLine(p1, p2, _tickPaint);
    }
  }

  @override
  bool shouldRepaint(_TickPainter oldDelegate) {
    return false;
  }
}
