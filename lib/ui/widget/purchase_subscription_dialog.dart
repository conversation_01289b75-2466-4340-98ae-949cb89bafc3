import 'package:flutter/material.dart';
import 'package:lovesync/api/requests/subscription_detail.dart';
import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/main.dart';
import 'package:lovesync/router.dart';
import 'package:provider/provider.dart';

import 'common_button.dart';

class PurchaseSubscriptionDialog {


  static Future<void> subscriptionDialog({
    required BuildContext context,
  }) async {
    await showDialog(
    context: context,
    builder: (BuildContext context) {
      APIService service = locator<APIService>();
      final user = Provider.of<UserModel>(navKey.currentContext!, listen: false);
      return AlertDialog(
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(15))),
        contentPadding: EdgeInsets.only(top: 12.0, bottom: 10.0),
        backgroundColor: Colors.white,
        title: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Icon(
                      Icons.cancel,
                      color: Colors.grey,
                    )),
              ],
            ),
            Container(
              height: 80,
              width: 80,
              decoration: BoxDecoration(
                  image: DecorationImage(image: AssetImage('assets/circle.png'))),
              child: Image.asset('assets/tick-circle.png'),
            ),
            SizedBox(
              height: 15,
            ),
            Center(
              child: Text(
                'Purchase Successful',
                style: TextStyle(
                    fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black),
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: MediaQuery.of(context).size.width,
          child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 15,
                  ),
                  Text(
                    "Enjoy the LoveSync+ features",
                    style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                        fontSize: 17),
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Row(
                    children: [
                      Text(
                        "•",
                        style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                            fontSize: 20),
                      ),
                      SizedBox(
                        width: 7,
                      ),
                      Text(
                        "Unlimited Syncs",
                        style: TextStyle(
                            color: Colors.black54,
                            fontWeight: FontWeight.w500,
                            fontSize: 15),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        "•",
                        style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                            fontSize: 20),
                      ),
                      SizedBox(
                        width: 7,
                      ),
                      Text(
                        "Up to 12 hr sync desire",
                        style: TextStyle(
                            color: Colors.black54,
                            fontWeight: FontWeight.w500,
                            fontSize: 15),
                      ),
                    ],
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "•",
                        style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                            fontSize: 20),
                      ),
                      SizedBox(
                        width: 7,
                      ),
                      Flexible(
                          child: Padding(
                              padding: EdgeInsets.only(top: 3),
                              child: Text(
                                "Sync score",
                                style: TextStyle(
                                    color: Colors.black54,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 15),
                              ))),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        "•",
                        style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                            fontSize: 20),
                      ),
                      SizedBox(
                        width: 7,
                      ),
                      Flexible(
                          child: Text(
                            "Private messaging",
                            style: TextStyle(
                                color: Colors.black54,
                                fontWeight: FontWeight.w500,
                                fontSize: 15),
                          )),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        "•",
                        style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                            fontSize: 20),
                      ),
                      SizedBox(
                        width: 7,
                      ),
                      Flexible(
                          child: Text(
                            "Remove ads",
                            style: TextStyle(
                                color: Colors.black54,
                                fontWeight: FontWeight.w500,
                                fontSize: 15),
                          )),
                    ],
                  ),
                ],
              )),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 17),
            child: CommonButton(
              //isProcess: isLoading,
                onPressed: () async {
                  final UserResponse userResponse = await service.request(SubscriptionDetailRequest());

                  await user.setData(userResponse).then((value) {
                    Navigator.pushNamed(context, RouteName.Home);

                  });
                  // Navigator.pushNamed(context, RouteName.Home);
                },
                name: 'Continue'),
          ),
          SizedBox(
            height: 10,
          ),
        ],
      );
    });
  }
}