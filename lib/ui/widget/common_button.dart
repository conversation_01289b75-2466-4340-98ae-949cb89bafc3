import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../colors.dart';

class CommonButton extends StatelessWidget {
  final void Function()? onPressed;
  final String name;
  final double? minWidth1;
  final double? height;
  final Color? color;
  final TextStyle? text1;
   bool isProcess = false;
   CommonButton({Key? key, required this.onPressed, required this.name, this.minWidth1, this.height, this.color, this.text1, this.isProcess = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialButton(
      color: color ?? allButtonColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      height: height ?? 45,
      minWidth: minWidth1,
      child: isProcess ?  CupertinoActivityIndicator(
        animating: true,
        color: Colors.white,
      ) :  Center(
        child:  Text(
          name,
          style: text1 ?? TextStyle(fontSize: 17,color: Colors.white,fontWeight: FontWeight.w500),
        ),
      ) ,
      onPressed: onPressed
    );
  }
}
