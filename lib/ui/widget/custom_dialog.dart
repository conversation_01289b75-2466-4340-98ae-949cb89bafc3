import 'package:flutter/material.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/locator.dart';

import '../../colors.dart';
import 'common_button.dart';

class CommonDialog {

  static void commonAlertShowDialog({
    required BuildContext context,
    String? dialogText,
    String? titleText,
    String? buttonText,
    String? buttonYesText,
    String? imagePath,
    String? decorationImagePath,
    double? fontSize,
    Border? border1,
    bool multipleButton = false,
    bool decorationImage = true,
    double? height,
    double? width,
    void Function()? onPressed,
    void Function()? onYes,
    void Function()? onNo,
    void Function()? onCancelIcon,
  }) async {
    await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(15))),
            contentPadding: EdgeInsets.only(top: 12.0, bottom: 10.0),
            backgroundColor: Colors.white,
            title: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    GestureDetector(
                        onTap: onCancelIcon ?? () => Navigator.of(context).pop(),
                        child: Icon(
                          Icons.cancel,
                          color: Colors.grey,
                        )),
                  ],
                ),
                imagePath != ""
                    ? Container(
                  height: 80,
                  width: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    border: border1,

                  ),
                  child: Center(
                      child: Image.asset(
                        imagePath ?? '',
                        height: height ?? 60,
                        width: width ?? 60,
                        fit: BoxFit.fill,
                      )),
                )
                    : Container(),
                SizedBox(
                  height: 15,
                ),
                Center(
                  child: Text(
                    titleText ?? '',
                    //'Enable a private pin',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            content: SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: Text(
                          dialogText ?? '',
                          textAlign: TextAlign.center,
                          //"LS+ utilizes 2-way encryption to ensure your messages and pics are seen only by you and your partner. Not even the app. creators will have access.",
                          style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w500, fontSize: fontSize ?? 14,),
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                    ],
                  )),
            ),
            actions: [
              if (multipleButton == true) ...{
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    CommonButton(
                      onPressed: onNo,
                      minWidth1: 115,
                      height: 45,
                      name: 'Cancel',
                      color: Color.fromRGBO(230, 230, 231, 1),
                      text1: TextStyle(fontSize: 15, color: Colors.black87),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    CommonButton(
                      onPressed: onYes,
                      minWidth1: 115,
                      height: 45,
                      name: buttonYesText ?? 'Yes, Delete!',
                      color: allButtonColor,
                      text1: TextStyle(fontSize: 15, color: Colors.white),
                    ),
                  ], //
                )
              },
              if (multipleButton == false) ...{
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 17),
                  child: CommonButton(
                      onPressed: onPressed,
                      // Navigator.pushNamed(context, RouteName.Home);

                      name: buttonText ?? 'Continue'),
                ),
              },
              SizedBox(
                height: 10,
              ),
            ],
          );
        });
  }
}


