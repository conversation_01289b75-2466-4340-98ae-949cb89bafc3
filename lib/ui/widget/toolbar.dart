import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/data/model/partner.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:provider/provider.dart';

import '../../api/requests/sync_score.dart';
import '../../locator.dart';

class Toolbar extends StatelessWidget {
  final Function(int)? onPageChange;
  final int? page;

  Toolbar({this.page, this.onPageChange});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 130,
      child: Card(
        elevation: 0.0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(24.0))),
        margin: EdgeInsets.all(24.0),
        color: colorNavPrimary,
        child: Container(
          // color: Colors.red,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Selector<UserModel, bool>(
                selector: (_, userModel) => userModel.isLoggedIn,
                builder: (_, isLoggedIn, __) {
                  return _buildToolbarItem(
                    context,
                    "Partner",
                    "assets/icons/partner.png",
                    "assets/icons/partner_active.png",
                    0,
                    isLoggedIn,
                  );
                },
              ),
              // SizedBox(width: 20.0),
              _buildToolbarItem(
                context,
                "LoveSync",
                "assets/icons/lovesync.png",
                "assets/icons/lovesync_active.png",
                1,
                true,
              ),
              // SizedBox(width: 20.0),
              Selector<UserModel, bool>(
                selector: (_, userModel) => userModel.isLoggedIn,
                builder: (_, isLoggedIn, __) {
                  return _buildToolbarItem(
                    context,
                    "Settings",
                    "assets/icons/settings.png",
                    "assets/icons/settings_active.png",
                    2,
                    isLoggedIn,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildToolbarItem(
      BuildContext context, String label, String iconPath, String iconActivePath, int index, bool visible) {
    AssetImage icon = AssetImage(iconPath);
    AssetImage iconActive = AssetImage(iconActivePath);

    precacheImage(icon, context);
    precacheImage(iconActive, context);

    bool isSelected = this.page == index;
    return Expanded(
      child: InkWell(
        overlayColor: MaterialStatePropertyAll(Colors.transparent),
        onTap: () async {
          UserModel userModel = Provider.of<UserModel>(context, listen: false);
          PartnerModel partnerModel = Provider.of<PartnerModel>(context, listen: false);
          if (isSelected && index == 1 && (!userModel.isLoggedIn || partnerModel.state != PartnerModelState.linked)) {
            MethodChannel methodChannel = MethodChannel("lovesync/onboarding");
            methodChannel.invokeMethod("launch");
          }
          if (!isSelected) {
            onPageChange!(index);
          }
          if (index == 0) {
            print('---====---');
            final service = locator<APIService>();
            final UserResponse userResponse = await service.request(SyncScoreRequest());
            UserModel userModel = Provider.of<UserModel>(context, listen: false);
            await userModel.setData(
              userResponse,
            );
          }
        },
        child: Visibility(
          visible: visible,
          child: Container(
            // color: colorNavItem,
            // margin: EdgeInsets.symmetric(vertical: 10.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  height: 24.0,
                  decoration: BoxDecoration(
                    image: DecorationImage(image: isSelected ? iconActive : icon),
                  ),
                ),
                SizedBox(height: 5.0),
                Text(label, style: TextStyle(fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
