import 'package:flutter/material.dart';
import 'package:lovesync/colors.dart';

class Button extends StatefulWidget {
  static const int THEME_DEFAULT = 0;
  static const int THEME_POSITIVE = 1;
  static const int THEME_NEGATIVE = 2;

  final String text;
  final int? theme;
  final VoidCallback onTap;
  final Widget? child;

  Button(this.text, {Key? key, this.theme, required this.onTap, this.child}) : super(key: key);

  @override
  ButtonState createState() => ButtonState();
}

class ButtonState extends State<Button> {
  double _opacity = 1;

  void _onPressStart() {
    setState(() {
      _opacity = 0.5;
    });
  }

  void _onPressEnd() {
    setState(() {
      _opacity = 1;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: widget.onTap,
        onTapDown: (_) => _onPressStart(),
        onTapUp: (_) => _onPressEnd(),
        onTapCancel: () => _onPressEnd(),
        child: Opacity(
            opacity: _opacity,
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: 54.0,
              child: Align(
                alignment: Alignment.center,
                child: widget.child ??
                    Text(
                      widget.text,
                      style: TextStyle(
                        color: _getTextColor(),
                        fontSize: 15.0,
                      ),
                    ),
              ),
              decoration: BoxDecoration(
                color: _getBackgroundColor(),
                borderRadius: BorderRadius.circular(24.0),
              ),
            )));
  }

  Color _getBackgroundColor() {
    switch (widget.theme) {
      case Button.THEME_POSITIVE:
        return colorButtonPositive;
      case Button.THEME_NEGATIVE:
        return colorButtonNegative;
      case Button.THEME_DEFAULT:
        return colorButtonDefault;
      default:
        return Colors.transparent;
    }
  }

  Color _getTextColor() {
    switch (widget.theme) {
      case Button.THEME_POSITIVE:
        return colorPrimary;
      case Button.THEME_NEGATIVE:
        return colorAccent;
      case Button.THEME_DEFAULT:
      default:
        return Colors.white;
    }
  }
}
