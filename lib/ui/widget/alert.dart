import 'package:flutter/material.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/ui/widget/button.dart';

class AlertBox extends StatelessWidget {
  static const YesNoActions = "YesNoActions";
  static const CloseActions = "CloseActions";

  final String title;
  final String subtitle;
  final String? type;

  AlertBox({required this.title, required this.subtitle, this.type});

  List<Widget> _getActions(BuildContext context) {
    switch (type) {
      case YesNoActions:
        return [
          But<PERSON>("Yes", onTap: () {
            Navigator.pop(context, true);
          }),
          SizedBox(height: 12),
          <PERSON><PERSON>("No", onTap: () {
            Navigator.pop(context, false);
          }),
        ];
      default:
        return [
          <PERSON><PERSON>("Close", onTap: () {
            Navigator.pop(context, false);
          }),
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: colorBackgroundDark,
      title: Text(title),
      content: Text(subtitle),
      actions: _getActions(context),
    );
  }
}
