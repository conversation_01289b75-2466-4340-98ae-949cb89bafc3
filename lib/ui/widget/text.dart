import 'package:flutter/material.dart';
import 'package:lovesync/colors.dart';

class LSText extends StatelessWidget {
  final String text;
  final LSTextStyle style;

  LSText(
    this.text,
    {
      this.style = LSTextStyle.Standard,
    }
  );

  @override
  Widget build(BuildContext context) {
    return Text(
      this.text,
      style: TextStyle(
        color: colorText,
        fontSize: 15.0,
      ),
    );
  }
}

enum LSTextStyle {
  Standard,
  Header,
  LargeHeader,
  Subheading,
}