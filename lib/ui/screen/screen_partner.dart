import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:lovesync/api/requests/cancel_invite.dart';
import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/data/model/partner.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/main.dart';
import 'package:lovesync/ui/widget/avatar.dart';
import 'package:lovesync/ui/widget/button.dart';
import 'package:lovesync/ui/widget/header.dart';
import 'package:lovesync/ui/widget/text.dart';
import 'package:lovesync/view/subscription_plan.dart';
import 'package:provider/provider.dart';

import '../../api/requests/reset_sync_score.dart';
import '../../api/requests/sync_score.dart';
import '../../api/requests/sync_score_unable.dart';
import '../../api/service.dart';
import '../../purchase_setup/purchase_api.dart';
import '../widget/common_button.dart';
import '../widget/custom_dialog.dart';

class PartnerScreen extends StatefulWidget {
  PartnerScreen({Key? key}) : super(key: key);

  @override
  _PartnerScreenState createState() => _PartnerScreenState();
  static bool isSyncScore = false;
  static int id = 0;
}

class _PartnerScreenState extends State<PartnerScreen> with AutomaticKeepAliveClientMixin<PartnerScreen> {
  @override
  bool get wantKeepAlive => true;

  bool syncScorePercentage = false;
  bool isPremium = false;
  String? syncScoreDate;
  String? resetScoreDate;
  bool isLoading = false;
  int lastSyncScore = 0;
  int currentSyncScore = 0;
  final service = locator<APIService>();

  @override
  void initState() {
    print('screen partner screen inistate');

    // if (mounted) {
    // WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
    UserModel userModel = Provider.of<UserModel>(context, listen: false);
    PartnerScreen.isSyncScore = userModel.data.isSyncUnable ?? false;
    syncScoreDate = DateFormat("dd MMM yyyy")
        .format(DateTime.parse(userModel.data.resetSyncScoreDate ?? DateTime.now().toString()));

    setSyncScore();
    super.initState();
    // });
    //}
  }

  setSyncScore() async {
    final UserResponse userResponse = await service.request(SyncScoreRequest());
    UserModel userModel = Provider.of<UserModel>(context, listen: false);
    await userModel.setData(
      userResponse,
    );
  }

  // Future<void> _updateLastSyncScore() async {
  //   UserModel userModel = Provider.of<UserModel>(context, listen: false);
  //  // currentSyncScore = userModel.data.successfulSyncPercentage ?? 0;
  //   currentSyncScore = userModel.data.successfulSyncPercentage ?? 0;
  //
  //   final int? storedLastSyncScore = prefs?.getInt('lastSyncScore') ?? 0;
  //
  //   print('storedLastSyncScore: $storedLastSyncScore');
  //   print('currentSyncScore: $currentSyncScore');
  //
  //   setState(() {
  //     WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
  //       if (storedLastSyncScore != null && storedLastSyncScore != currentSyncScore) {
  //         // If last sync score is already stored and different from the current sync score, update it
  //         print('if storedLastSyncScore: $currentSyncScore');
  //         lastSyncScore = storedLastSyncScore;
  //         // Update the stored value to the current sync score
  //         await prefs?.setInt('lastSyncScore', currentSyncScore);
  //       } else {}
  //       });
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Container(
      child: ListView(
        physics: ClampingScrollPhysics(),
        padding: EdgeInsets.fromLTRB(36.0, 30.0, 36.0, 200.0),
        shrinkWrap: true,
        children: <Widget>[
          Header('My Partner'),
          SizedBox(height: 10.0),
          LSText('Turn missed opportunities into more sex by knowing when your moods are matched.'),
          SizedBox(height: 50.0),
          Consumer<PartnerModel>(
            builder: (context, partnerModel, child) =>
                _renderSync(MediaQuery.of(context).size.width, partnerModel, context),
          ),

          // Text(
          //   'Tips & Tricks',
          //   style: TextStyle(
          //     color: Color(0xFF999FA7),
          //     fontSize: 15.0,
          //     fontWeight: FontWeight.w500,
          //   ),
          // ),
          // SizedBox(height: 8.0),
          // Text(
          //   'Tap LoveSync anytime throughout the day when the mood strikes. You might be surprised how often you and your partner are in sync!',
          //   style: TextStyle(
          //     color: colorText,
          //     fontSize: 15.0,
          //   ),
          // )
        ],
      ),
    );
  }

  Widget _renderSync(double size, PartnerModel partnerModel, BuildContext context) {
    PartnerModel partnerModel = Provider.of<PartnerModel>(context, listen: false);
    return Container(
      width: size,
      height: size * 1.3,
      child: Stack(
        children: <Widget>[
          Container(
            width: size,
            child: Column(
              children: <Widget>[
                Selector<PartnerModel, bool>(
                  selector: (_, partnerModel) => partnerModel.isLinked,
                  builder: (_, isLinked, __) {
                    if (isLinked) {
                      return _renderProfileLink(size);
                    } else {
                      return _renderProfileAddLink(size);
                    }
                  },
                ),
                SizedBox(height: size / 10.0),
                Selector<PartnerModel, PartnerModelState>(
                  selector: (_, partnerModel) => partnerModel.state ?? PartnerModelState.invitation,
                  builder: (_, state, __) {
                    if (state == PartnerModelState.linked) {
                      return _renderSyncLabel();
                    } else if (state == PartnerModelState.invited) {
                      print("partner =======>");
                      return _renderPendingLabel(context);
                    } else if (state == PartnerModelState.twoInvited) {
                      //print("partner1 =======>");
                      return _renderPendingLabel(context);
                    } else {
                      // print("partner2 =======>");
                      return _renderAddSyncLabel(context);
                    }
                  },
                ),
              ],
            ),
          ),
          Visibility(
            visible: partnerModel.isLinked,
            child: Lottie.asset('assets/lottie/hearts.json', animate: partnerModel.isLinked),
          ),
        ],
      ),
    );
  }

  Widget _renderPendingLabel(BuildContext context) {
    return Column(children: <Widget>[
      SizedBox(height: 8.0),
      Selector<PartnerModel, String>(
        selector: (_, partnerModel) {
          if (partnerModel.data?.email == null) {
            return '';
          }
          return partnerModel.data1?.email ?? 'test';
        },
        builder: (_, email, __) {
          return Text(
            'Waiting for confirmation from $email...',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: colorText,
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          );
        },
      ),
      Padding(
        padding: const EdgeInsets.only(top: 12),
        child: Button(
          'Cancel invite',
          theme: Button.THEME_NEGATIVE,
          onTap: () async {
            final service = locator<APIService>();
            PartnerModel partnerModel = Provider.of<PartnerModel>(context, listen: false);

            try {
              await service.request(CancelInviteRequest());
              await partnerModel.setState(null);
              await partnerModel.setData(null);
            } catch (err) {
              print(err);
            }
          },
        ),
      )
    ]);
  }

  Widget _renderSyncLabel() {
    UserModel userModel = Provider.of<UserModel>(context, listen: false);
    PartnerScreen.id = userModel.data.id ?? 0;
    bool isScoreIncreased = int.parse(userModel.data.successfulSyncPercentage.toString()) >
        int.parse(userModel.data.previousSyncScore.toString());

    return Column(
      children: <Widget>[
        Selector<PartnerModel, String>(
          selector: (_, partnerModel) {
            if (partnerModel.data == null) {
              return '';
            }
            return partnerModel.data?.username ?? '';
          },
          builder: (_, username, __) {
            return LSText('You and $username are');
          },
        ),
        SizedBox(height: 8.0),
        Text(
          'connected',
          style: TextStyle(
            color: colorText,
            fontSize: 16.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.0),
        Selector<PartnerModel, String>(
          selector: (_, partnerModel) {
            if (partnerModel.data == null) {
              return '';
            }
            return partnerModel.data?.email ?? '';
          },
          builder: (_, email, __) {
            return Text(
              email,
              style: TextStyle(
                color: colorTextAccent,
                fontSize: 14.0,
              ),
            );
          },
        ),
        SizedBox(
          height: 30,
        ),
        SizedBox(
          height: 10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            Padding(
              // padding: const EdgeInsets.symmetric(horizontal: 70),
              padding: EdgeInsets.symmetric(horizontal: MediaQuery.of(context).size.width * 0.20),
              child: Text(
                'Sync Score',
                style: TextStyle(
                  color: colorText,
                  fontSize: 17.0,
                ),
              ),
            ),
            FlutterSwitch(
              width: 50,
              height: 25,
              toggleSize: 27,
              value: userModel.data.premiumName == planName ? false : PartnerScreen.isSyncScore,
              borderRadius: 30,
              padding: 2,
              activeColor: Color(0xFFFF7D80),
              inactiveColor: Colors.grey,
              activeToggleColor: Colors.white,
              onToggle: userModel.data.premiumName == planName
                  ? (value) {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => SubscriptionPlanScreen(),
                          ));
                    }
                  : (value) async {
                      setState(() {
                        PartnerScreen.isSyncScore = value;
                      });
                      if (value == false) {
                        CommonDialog.commonAlertShowDialog(
                            context: context,
                            height: 80,
                            width: 80,
                            dialogText:
                                'Both you and your partner will no longer be able to view your score.\n\nNote: This will not remove sync data or reset the score. Use "Reset" if you would like to restart the score calculation',
                            multipleButton: true,
                            buttonYesText: "Disable",
                            imagePath: 'assets/Delete account Popup.png',
                            onCancelIcon: () async {
                              setState(() {
                                PartnerScreen.isSyncScore = true;
                              });
                              final UserResponse userResponse = await service.request(SyncScoreUnableRequest(true));
                              final UserResponse userResponse2 = await service.request(SyncScoreRequest());

                              await userModel.setData(
                                userResponse,
                              );
                              await userModel.setData(
                                userResponse2,
                              );

                              syncScoreDate = DateFormat("dd MMM yyyy").format(
                                  DateTime.parse(userModel.data.resetSyncScoreDate ?? DateTime.now().toString()));
                              print("user sync =======> ${userModel.data.isSyncUnable}");
                              print("user sync =======> ${userModel.data.isPartnerSyncUnable}");

                              Navigator.pop(context);
                              setState(() {});
                            },
                            onNo: () async {
                              setState(() {
                                PartnerScreen.isSyncScore = true;
                              });
                              final UserResponse userResponse = await service.request(SyncScoreUnableRequest(true));
                              final UserResponse userResponse2 = await service.request(SyncScoreRequest());

                              await userModel.setData(
                                userResponse,
                              );
                              await userModel.setData(
                                userResponse2,
                              );

                              syncScoreDate = DateFormat("dd MMM yyyy").format(
                                  DateTime.parse(userModel.data.resetSyncScoreDate ?? DateTime.now().toString()));
                              print("user sync =======> ${userModel.data.isSyncUnable}");
                              print("user sync =======> ${userModel.data.isPartnerSyncUnable}");

                              Navigator.pop(context);
                              setState(() {});
                            },
                            onYes: () async {
                              setState(() {
                                PartnerScreen.isSyncScore = false;
                              });
                              final UserResponse userResponse = await service.request(SyncScoreUnableRequest(false));
                              final UserResponse userResponse2 = await service.request(SyncScoreRequest());

                              await userModel.setData(
                                userResponse,
                              );
                              await userModel.setData(
                                userResponse2,
                              );

                              syncScoreDate = DateFormat("dd MMM yyyy").format(
                                  DateTime.parse(userModel.data.resetSyncScoreDate ?? DateTime.now().toString()));
                              print("user sync =======> ${userModel.data.isSyncUnable}");
                              print("user sync =======> ${userModel.data.isPartnerSyncUnable}");

                              Navigator.pop(context);
                              setState(() {});
                            },
                            titleText: "Are you sure you want to reset the sync score?",
                            buttonText: "Yes");
                      } else {
                        final UserResponse userResponse =
                            await service.request(SyncScoreUnableRequest(PartnerScreen.isSyncScore));
                        final UserResponse userResponse2 = await service.request(SyncScoreRequest());

                        await userModel.setData(userResponse);
                        await userModel.setData(userResponse2);

                        syncScoreDate = DateFormat("dd MMM yyyy")
                            .format(DateTime.parse(userModel.data.resetSyncScoreDate ?? DateTime.now().toString()));
                        print("user sync =======> ${userModel.data.isSyncUnable}");
                        print("user sync =======> ${userModel.data.isPartnerSyncUnable}");
                      }
                      final UserResponse userResponse = await service.request(SyncScoreRequest());
                      userModel = Provider.of<UserModel>(context, listen: false);
                      await userModel.setData(
                        userResponse,
                      );
                      setState(() {});
                    },
            ),
          ],
        ),
        SizedBox(
          height: 10,
        ),
        if (userModel.data.premiumName != planName &&
            userModel.data.isSyncUnable == true &&
            userModel.data.isPartnerSyncUnable == true) ...[
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "${userModel.data.successfulSyncPercentage ?? 0}%",
                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500, fontSize: 20),
                  ),
                  SizedBox(
                    width: 3,
                  ),
                  (userModel.data.successfulSyncPercentage != 0)
                      ? Icon(
                          isScoreIncreased ? Icons.arrow_upward : Icons.arrow_downward,
                          color: isScoreIncreased ? Colors.green : Colors.red,
                        )
                      : Container(),
                ],
              ),
              SizedBox(
                height: 10,
              ),
              Text(
                syncScorePercentage == true ? 'Since $resetScoreDate ' : 'Since $syncScoreDate',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500, fontSize: 17),
              ),
              SizedBox(
                height: 20,
              ),
              CommonButton(
                name: 'Reset',
                minWidth1: double.infinity,
                isProcess: isLoading,
                onPressed: () async {
                  CommonDialog.commonAlertShowDialog(
                      context: context,
                      dialogText:
                          "Are you sure you want to reset the sync score? This will restart the score calculation back at 0% for you and your partner.",
                      multipleButton: true,
                      buttonYesText: "Reset score",
                      imagePath: 'assets/Delete account Popup.png',
                      height: 80,
                      width: 80,
                      onNo: () {
                        Navigator.pop(context);
                      },
                      onYes: () async {
                        setState(() {
                          isLoading = true;
                        });
                        syncScorePercentage = true;
                        UserResponse userResponse = await service.request(ResetSyncScoreRequest(userModel.data.id));
                        await userModel.setData(
                          userResponse,
                        );

                        userResponse = await service.request(SyncScoreRequest());
                        userModel = Provider.of<UserModel>(context, listen: false);
                        await userModel.setData(
                          userResponse,
                        );

                        resetScoreDate = DateFormat("dd MMM yyyy")
                            .format(DateTime.parse(userModel.data.resetSyncScoreDate ?? DateTime.now().toString()));
                        print("userModel.data.resetedDate ${userModel.data.resetSyncScoreDate}");
                        currentSyncScore = 0;
                        prefs?.setInt('lastSyncScore', 0);

                        setState(() {
                          isLoading = false;
                        });
                        Navigator.pop(context);
                        print("date ======> $resetScoreDate");
                      },
                      titleText: "Reset Sync Score",
                      buttonText: "Yes");
                },
              )
            ],
          )
        ] else if (userModel.data.premiumName != planName &&
            userModel.data.isSyncUnable == true &&
            userModel.data.isPartnerSyncUnable == false) ...[
          Padding(
            padding: EdgeInsets.only(top: 20),
            child: Text(
              'Waiting for partner to enable sync score',
              style: TextStyle(
                color: colorText,
                fontSize: 14,
              ),
            ),
          ),
        ] else if (userModel.data.premiumName == planName) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "- - ",
                style: TextStyle(color: Colors.grey, fontWeight: FontWeight.w500, fontSize: 20),
              ),
              Text(
                "%",
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500, fontSize: 20),
              ),
            ],
          ),
          // Stack(
          //   clipBehavior: Clip.none,
          //   children: [
          //     Container(
          //       padding: EdgeInsets.only(top: 16),
          //       child: Container(
          //         height: 90,
          //         width: MediaQuery.of(context).size.width,
          //         decoration: BoxDecoration(
          //             border: Border.all(color: Color(0xFFFF7D80)), borderRadius: BorderRadius.circular(5)),
          //         child: Padding(
          //           padding: const EdgeInsets.only(top: 7),
          //           child: Column(
          //             mainAxisAlignment: MainAxisAlignment.center,
          //             children: [
          //               Row(
          //                 mainAxisAlignment: MainAxisAlignment.spaceAround,
          //                 children: [
          //                   Image.asset(
          //                     'assets/premium_icon.png',
          //                     height: 45,
          //                     width: 45,
          //                   ),
          //                   Column(
          //                     crossAxisAlignment: CrossAxisAlignment.start,
          //                     children: [
          //                       Text(
          //                         "Try LoveSync+",
          //                         style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 16),
          //                       ),
          //                       SizedBox(
          //                         height: 5,
          //                       ),
          //                       Text(
          //                         "for 30 days",
          //                         style: TextStyle(
          //                             height: 1, color: Colors.white, fontWeight: FontWeight.w500, fontSize: 14),
          //                       ),
          //                     ],
          //                   ),
          //                   CommonButton(
          //                     onpressed: () {
          //                       Navigator.push(
          //                           context,
          //                           MaterialPageRoute(
          //                             builder: (context) => SubscriptionPlanScreen(),
          //                           ));
          //                     },
          //                     name: 'Try now',
          //                     minWidth1: 20,
          //                     height: 30,
          //                   )
          //                 ],
          //               ),
          //             ],
          //           ),
          //         ),
          //       ),
          //     ),
          //     Center(
          //       child: Container(
          //         height: 32,
          //         width: 100,
          //         decoration: BoxDecoration(
          //             border: Border.all(color: Color(0xFFFF7D80)),
          //             color: Color(0xFFFF7D80),
          //             borderRadius: BorderRadius.circular(20)),
          //         child: Center(
          //           child: Text(
          //             "LoveSync+",
          //             style: TextStyle(color: Colors.white, fontWeight: FontWeight.w700, fontSize: 15),
          //           ),
          //         ),
          //       ),
          //     ),
          //   ],
          // )
        ] else ...[
          Container()
        ]
      ],
    );
  }

  Widget _renderAddSyncLabel(BuildContext context) {
    return Column(children: <Widget>[
      Button(
        'Add Partner',
        theme: Button.THEME_NEGATIVE,
        onTap: () {
          Navigator.pushReplacementNamed(context, 'linkPartner');
        },
      )
    ]);
  }

  Widget _renderProfileLink(double parentWidth) {
    double width = parentWidth * (2.0 / 3.0);
    double height = width / 2.0;
    return Container(
      width: width,
      height: height,
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Row(
            children: <Widget>[
              Selector<UserModel, String>(
                selector: (_, userModel) {
                  if (userModel.data == null) {
                    return 'null';
                  }
                  return userModel.data.image ?? '';
                },
                builder: (_, image, __) {
                  return Avatar(image: image, size: height);
                },
              ),
              Selector<PartnerModel, String>(
                selector: (_, partnerModel) {
                  if (partnerModel.data == null) {
                    return 'null';
                  }
                  return partnerModel.data?.image ?? '';
                },
                builder: (_, image, __) {
                  return Avatar(image: image, size: height);
                },
              ),
            ],
          ),
          Container(
            width: 44.0,
            height: 44.0,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromARGB(80, 0, 0, 0),
                  spreadRadius: 0,
                  blurRadius: 10,
                ),
              ],
            ),
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(image: AssetImage('assets/icons/link.png')),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _renderProfileAddLink(double parentWidth) {
    double width = parentWidth * (2.0 / 3.0);
    double height = width / 2.0;
    return Container(
      width: width,
      height: height,
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Row(
            children: <Widget>[
              Selector<UserModel, String>(
                selector: (_, userModel) {
                  if (userModel.data == null) {
                    return '';
                  }
                  return userModel.data.image ?? '';
                },
                builder: (_, image, __) {
                  return Avatar(image: image, size: height);
                },
              ),
              Avatar(image: '', size: height),
            ],
          ),
          Container(
            width: 44.0,
            height: 44.0,
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(image: AssetImage('assets/icons/add.png')),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
