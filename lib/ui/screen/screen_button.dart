import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:lovesync/analytics.dart';
import 'package:lovesync/api/requests/delete_sync.dart';
import 'package:lovesync/api/requests/get_sync.dart';
import 'package:lovesync/api/requests/send_message.dart';
import 'package:lovesync/api/requests/start_sync.dart';
import 'package:lovesync/api/requests/sync.dart';
import 'package:lovesync/api/requests/sync_score.dart';
import 'package:lovesync/api/requests/update_invite.dart';
import 'package:lovesync/api/responses/sync.dart';
import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/data/model/app.dart';
import 'package:lovesync/data/model/partner.dart';
import 'package:lovesync/data/model/sync.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/router.dart';
import 'package:lovesync/ui/widget/button.dart';
import 'package:lovesync/ui/widget/sync.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../../ad_helper.dart';
import '../../main.dart';
import '../../purchase_setup/purchase_api.dart';
import '../widget/common_button.dart';
import '../widget/custom_dialog.dart';

late double globalDx;
late double globalDy;

class ButtonScreen extends StatefulWidget {
  ButtonScreen({
    Key? key,
  }) : super(key: key);

  @override
  _ButtonScreenState createState() => _ButtonScreenState();
  static String drpDownValues = '';
  static String customMessage = '';
  static int userid = 0;
 static   int count = 1;
 static Map<int, int> idCounts = {};
}

class _ButtonScreenState extends State<ButtonScreen>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin<ButtonScreen>, WidgetsBindingObserver {
  final _avatarSecondaryPosition = GlobalKey();
  final _sync = GlobalKey();

  late bool _syncInteractLock;
  late bool _syncDown;
  String _errorMessage = "";

  late int _minutes;
  late String _durationText;
  late String _buttonTimeText;
  late double _buttonFraction;
  late SyncState _buttonSyncState;
  Color _glowColor = colorAccent;
  Timer? _colorChangeTimer;
  Timer? _timer;
  late AnimationController? _fractionAnimator;
  late int _buttonDownAt;
  SyncResponse? _syncSent;
  late double _headerPaddingLeft;
  bool isPlanExpire = false;

  bool isSelectNudge = false;

  late bool _avatarVisible;
  late Duration _avatarDuration;
  double? _avatarSize;
  late double _avatarDx;
  late double _avatarDy;
  final nudgeController = TextEditingController();

//
  Timer? syncConfirmTimer;
  Duration debounceDuration = Duration(seconds: 4);

  late TutorialCoachMark tutorialCoachMark;
  List<TargetFocus> targets = [];
  String? dropdownValue;

  List<String> nudge = [
    'Thinking of you 🥰',
    'Craving you 😘',
    'Can’t wait to see you!',
    'Hi there',
  ];

  List<Widget> imageWidgets = [];
  final GlobalKey<FormFieldState> _key = GlobalKey<FormFieldState>();
  Timer? _timer1;

  late BannerAd _bannerAd;

  bool isBannerAdReady = false;

  bool isLoading = false;

  Future<void> getData() async {}

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _syncInteractLock = false;
    _syncDown = false;

    _minutes = 0;
    _durationText = '...';
    _buttonTimeText = '';
    _buttonFraction = 0.0;

    _avatarVisible = true;
    _avatarDx = 0.0;
    _avatarDy = 0.0;
    _avatarDuration = Duration(milliseconds: 400);

    _headerPaddingLeft = 0.0;
    ButtonScreen.drpDownValues = dropdownValue ?? '';
    ButtonScreen.customMessage = nudgeController.text;
    //ButtonScreen.isMessage = isSelectNudge;

    _bannerAd = BannerAd(
        size: AdSize.banner,
        adUnitId: AdHelper.bannerAdUnitId,
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            setState(() {
              isBannerAdReady = true;
            });
            print("test  ======>");
          },
          onAdFailedToLoad: (ad, error) {
            print('Failed to load a banner ad: ${error.message}');
            print("error ====> $error");
            isBannerAdReady = false;
            ad.dispose();
          },
        ),
        request: AdRequest())
      ..load();

    AppModel appModel = Provider.of<AppModel>(context, listen: false);
    UserModel userModel = Provider.of<UserModel>(context, listen: false);

    PartnerModel partnerModel = Provider.of<PartnerModel>(context, listen: false);
    ButtonScreen.userid = partnerModel.data?.id ?? 0;
    SyncModel syncModel = Provider.of<SyncModel>(context, listen: false);

    if (userModel.isLoggedIn) {
      _buttonSyncState = SyncState.OFF;
      _syncSent = syncModel.sentActive;
      print("isLoggedIn called0");
      if (_syncSent != null && _syncSent?.expiresAt != null && _syncSent!.expiresAt.isAfter(DateTime.now())) {
        print("called1");

        Duration diff = _syncSent!.expiresAt.difference(DateTime.now());
        int minutes = diff.inMinutes;

        if (minutes <= 15) {
          _buttonFraction = 0.25 * (minutes / 15.0);
        } else if (minutes <= 60) {
          _buttonFraction = 0.50 * (minutes / 60.0);
        } else if (minutes <= 300) {
          _buttonFraction = 0.75 * (minutes / 300.0);
        } else {
          userModel.data.premiumName == planName
              ? _buttonFraction = minutes / 480.0
              : _buttonFraction = minutes / 720.0;
        }
        _minutes = minutes;
        _buttonSyncState = SyncState.ON;
        _durationText = _getExpiryString(_syncSent!.expiresAt.toLocal());
      } else if (_syncSent != null && _syncSent!.isSync) {
        print("called2");
        _minutes = 0;
        _buttonSyncState = SyncState.ON;
        _durationText = _getExpiryString(_syncSent!.expiresAt.toLocal());
      } else {
        print("called3");
        _avatarSize = 80.0;
        _avatarDuration = Duration(milliseconds: 0);
        WidgetsBinding.instance.addPostFrameCallback((Duration value) {
          _moveAvatarHeader();
        });
      }
    } else {
      _avatarVisible = false;
      _buttonSyncState = SyncState.OFF;
    }

    if (!partnerModel.isLinked) {
      _avatarVisible = false;
      _headerPaddingLeft = 0.0;
    } else {
      _avatarVisible = true;
      _headerPaddingLeft = _avatarSize == 80.0 ? 96.0 : 0.0;
    }

    WidgetsBinding.instance.addObserver(this);

    void _loadAdAndShowTutorial() async {
      // Load the ad
      await _bannerAd.load();

      // Delay for 500 milliseconds
      await Future.delayed(Duration(milliseconds: 700));

      // Show the tutorial
      _showTutorial();
    }

    _initTargets();

    if (partnerModel.isLinked && !appModel.isTutorialFinished) {
      appModel.setTutorialFinished();
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        Future.delayed(Duration(milliseconds: 500), () {
          _loadAdAndShowTutorial();
        });
      });
    }
  }

  void _initTargets() {
    targets.add(
      TargetFocus(
        identify: 'Target 0',
        keyTarget: _sync,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            child: Container(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Press the LoveSync button whenever you\'re in the mood.',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 20.0,
                    ),
                  ),
                  SizedBox(height: 10.0),
                  Text(
                    'Tap multiple times or press and hold to set how long you\'d be good to go',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // void startTimer(Duration duration) {
  //   print('duration --->$duration');
  //   _timer1 = Timer(duration, () {
  //     // Reset function call
  //     _reset();
  //              _timer1!.cancel();
  //   });
  // }

  // void startTimer() {
  //   _timer1 = Timer.periodic(Duration(seconds: 5), (timer) {
  //     if (_syncSent != null) {
  //       // Convert expiration time to local time zone
  //       DateTime expiresAtLocal = _syncSent!.expiresAt.toLocal();
  //
  //       print('timer1 ----- Expires At: $expiresAtLocal (Time Zone: ${expiresAtLocal.timeZoneName})');
  //       print('timer1 ----- Current Time: ${DateTime.now()} (Time Zone: ${DateTime.now().timeZoneName})');
  //
  //       if (expiresAtLocal.isBefore(DateTime.now())) {
  //         print('Expiry time has passed.');
  //         _reset();
  //         _timer1!.cancel();
  //       }
  //     }
  //   });
  // }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
    _bannerAd.dispose();
  }

  @override
  didChangeDependencies() {
    super.didChangeDependencies();
    print("Change =====>");
    SyncModel syncModel = Provider.of<SyncModel>(context, listen: true);
    print("waitingSync =====>${syncModel.waitingSync}");
    print("started =====>${syncModel.sentActive?.started}");
    print("Change =====>${syncModel.sentActive?.expiresAt.isAfter(DateTime.now())}");
    SharedPreferences sharedPreferences = locator<SharedPreferences>();
    print("Change =====>sync  ${sharedPreferences.getString(
      'sync',
    )}");

    if (syncModel.sentActive?.status != 'waiting') {
      print("Change =====>sync  1 ${sharedPreferences.getString(
        'sync',
      )}");
      print("Change =====>sync  1 ${sharedPreferences.getString(
        'sync',
      )}");
      //   if(_avatarSize != 80) {
      //    _reset(false);
      // }
    }
    if (syncModel.waitingSync == false &&
        syncModel.sentActive?.started == true &&
        syncModel.sentActive?.expiresAt.isAfter(DateTime.now()) == false) {
      if (_avatarSize != 80.0) {
        print('reset aaaaa before$_avatarSize');
        _reset(false);
        print('reset aaaaa after$_avatarSize');
      }
    }

    if (syncModel.sentActive != _syncSent) {
      _syncSent = syncModel.sentActive;
      print('-------');

      if (_syncSent == null) {
        print('_syncSent ------- _syncSent');
        setState(() {
          _minutes = 0;
          _buttonSyncState = SyncState.OFF;
          _durationText = '...';
          _buttonTimeText = '';
          _buttonFraction = 0.0;
        });

        // WidgetsBinding.instance.addPostFrameCallback((Duration value) {
        //   _moveAvatarHeader();
        // });

        _moveAvatarHeader();
        _stopColorAnimation();
      } else {
        // if(_syncSent!.started && syncModel.sentActive?.status == 'expired') {
        //     if(_avatarSize != 80) {
        //        print('-------80${_avatarSize} ');
        //      _reset(false);
        //   }
        // }
        if (_syncSent!.started) {
          print('-------_syncSent started');
          print('-------__buttonSyncState');
          _moveAvatarButton();
          setState(() {
            _buttonSyncState = SyncState.ON;
            _durationText = _getExpiryString(_syncSent!.expiresAt.toLocal());
            _headerPaddingLeft = 0.0;
          });
          //_startColorAnimation();
        }
        if (_syncSent!.isSync) {
          print('-------_syncSent_syncSent_syncSent');

          _startColorAnimation();
        }
      }
    }

    if (syncModel.synced && _colorChangeTimer == null) {
      print('-------_syncSent_syncSent_syncSent _colorChangeTimer');

      _startColorAnimation();
    }

    UserModel userModel = Provider.of<UserModel>(context, listen: true);
    PartnerModel partnerModel = Provider.of<PartnerModel>(context, listen: true);
    if (!userModel.isLoggedIn) {
      setState(() {
        _avatarVisible = false;
        _headerPaddingLeft = 0.0;
      });
    } else {
      if (!partnerModel.isLinked) {
        setState(() {
          _avatarVisible = false;
          _headerPaddingLeft = 0.0;
        });
      } else {
        setState(() {
          _avatarVisible = true;
          _headerPaddingLeft = _avatarSize == 80.0 ? 96.0 : 0.0;
        });
        if (_avatarSize == 80.0) {
          WidgetsBinding.instance.addPostFrameCallback((Duration value) {
            print("called in change dependency (set profile position)");
            //_moveAvatarHeader();
          });
        }
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      print('asasassa');
      _stopPress();
      _syncInteractLock = false;
      _syncDown = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    print("screen button");

    PartnerModel partnerModel = Provider.of<PartnerModel>(context, listen: false);

    UserModel userModel = Provider.of<UserModel>(context, listen: true);

    super.build(context);
    var horizontalPadding = 50.0;
    return Container(
      //padding: EdgeInsets.fromLTRB(horizontalPadding, 30.0, horizontalPadding, 106.0),
      child: (partnerModel.isLinked && userModel.data.premiumName == planName)
          ? Column(
              //mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Column(
                  children: [
                    if (isBannerAdReady) ...{
                      Container(
                        height: _bannerAd.size.height.toDouble(),
                        width: _bannerAd.size.width.toDouble(),
                        // height: 50,
                        // width: 50,
                        decoration: BoxDecoration(color: Colors.white),
                        child: AdWidget(ad: _bannerAd),
                      ),
                    } else ...{
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 20),
                        // height: _bannerAd.size.height.toDouble(),
                        // width: _bannerAd.size.width.toDouble(),
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(color: Colors.white),
                        child: Center(
                            child: isBannerAdReady
                                ? CircularProgressIndicator(
                                    color: Color(0xFF5BFBA7),
                                  )
                                : Text(
                                    'Ads not available',
                                    style: TextStyle(color: Colors.black),
                                  )),
                        // child: AdWidget(ad: _bannerAd),
                      ),
                    }
                  ],
                ),
                Expanded(
                  child: Padding(
                    padding: (userModel.data.premiumName == planName && Platform.isIOS)
                        ? EdgeInsets.fromLTRB(50, 0.0, 50, 56.0)
                        : (userModel.data.premiumName == planName && Platform.isAndroid)
                            ? EdgeInsets.fromLTRB(50, 0, 50, 70)
                            : EdgeInsets.fromLTRB(horizontalPadding, 30.0, horizontalPadding, 106.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        _getUpper(),
                        _getButton(),
                        _getLower(),
                      ],
                    ),
                  ),
                ),
              ],
            )
          : Padding(
              padding: EdgeInsets.fromLTRB(horizontalPadding, 30.0, horizontalPadding, 106.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  _getUpper(),
                  _getButton(),
                  _getLower(),
                ],
              ),
            ),
    );
  }

  // Upper widgets:

  Widget _getUpper() {
    return Container(
      height: 110.0,
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.symmetric(vertical: 10),
      child: Selector<PartnerModel, PartnerModelState>(
        selector: (_, partnerModel) => partnerModel.state ?? PartnerModelState.linked,
        builder: (_, state, __) {
          if (state == PartnerModelState.invitation) {
            return _getInvitation();
          } else if (state == PartnerModelState.twoInvited) {
            return _getInvitation();
          } else {
            return _getHeader();
          }
        },
      ),
    );
  }

  Widget _getHeader() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final partnerModel = Provider.of<PartnerModel>(context, listen: false);

    return Container(
      margin: EdgeInsets.symmetric(vertical: partnerModel.isLinked ? 15 : 0),
      child: Stack(
        children: <Widget>[
          Padding(
            padding: EdgeInsets.only(
                top: (userModel.data.premiumName == planName && Platform.isAndroid)
                    ? 20
                    : (userModel.data.premiumName == planName && Platform.isIOS)
                        ? 10
                        : 0),
            child: Consumer3<UserModel, PartnerModel, SyncModel>(
              builder: (_, userModel, partnerModel, syncModel, __) {
                String title = '';
                String subtitle = '';

                double titleSize = 25.0;
                double subtitleSize = 14.0;

                if (!userModel.isLoggedIn) {
                  title = 'Welcome!';
                  subtitle = 'Click the button below to get started';
                } else {
                  if (partnerModel.isLinked) {
                    if (syncModel.synced) {
                      print("syncModel==> ${syncModel.synced}");
                      // print('oh yeah');
                      title = 'Oh yeah!';
                      subtitle = 'You\'re sync\'d with ${partnerModel.data?.username}';
                    } else if (syncModel.waitingSync && syncModel.sentActive!.started) {
                      print("waitingSync ${syncModel.waitingSync}");
                      title = 'Hang tight!';
                      subtitle = 'Waiting to see if ${partnerModel.data?.username} is thinking like you';
                    } else {
                      print("for getting down with ${syncModel.waitingSync}");

                      title = 'I’m up for getting down with';
                      subtitle = partnerModel.data?.username ?? '';
                      titleSize = 14.0;
                      subtitleSize = 25.0;
                      // _moveAvatarHeader();
                    }
                  } else {
                    if (partnerModel.isInvited) {
                      title = 'Get Ready!';
                      subtitle = 'Just waiting on your partner to accept before LoveSync can be used.';
                    } else {
                      print("add partner");
                      title = 'Add Partner';
                      subtitle = 'It takes two to make love. Add your partner to use LoveSync.';
                    }
                  }
                }
                return AnimatedPadding(
                  padding: EdgeInsets.only(left: _headerPaddingLeft),
                  duration: Duration(milliseconds: 300),
                  curve: Curves.ease,
                  child: partnerModel.isLinked
                      ? FittedBox(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(title, style: TextStyle(fontSize: titleSize)),
                              Text(subtitle, style: TextStyle(fontSize: subtitleSize)),
                            ],
                          ),
                        )
                      : Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(title, style: TextStyle(fontSize: titleSize)),
                            Text(subtitle, style: TextStyle(fontSize: subtitleSize)),
                          ],
                        ),
                );
              },
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Container(
                key: _avatarSecondaryPosition,
                width: 80.0,
                height: 80.0,
                //color: Colors.red,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _getInvitation() {
    return Column(
      children: <Widget>[
        Selector<PartnerModel, String>(
          selector: (_, partnerModel) {
            if (partnerModel.data == null) {
              return '';
            }
            return partnerModel.data?.email ?? '';
          },
          builder: (_, email, __) {
            print("You have a new invite from ========>");
            return Text('You have a new invite from $email');
          },
        ),
        SizedBox(height: 12),
        Row(
          children: <Widget>[
            Expanded(
              child: Button('Accept', theme: Button.THEME_POSITIVE, onTap: () {
                _updateInvitation(true);
              }),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Button('Decline', theme: Button.THEME_NEGATIVE, onTap: () {
                _updateInvitation(false);
              }),
            ),
          ],
        )
      ],
    );
  }

  // Center widgets:

  Widget _getButton() {
    return Expanded(
      child: Center(
        child: AspectRatio(
          aspectRatio: 1.0,
          child: LayoutBuilder(builder: (context, constraints) {
            return Stack(
              children: <Widget>[
                Sync(
                  key: _sync,
                  size: constraints.maxWidth,
                  fraction: _buttonFraction,
                  time: _buttonTimeText,
                  state: _buttonSyncState,
                  glowColor: _glowColor,
                  avatarDuration: _avatarDuration,
                  avatarVisible: _avatarVisible,
                  avatarDx: _avatarDx,
                  avatarDy: _avatarDy,
                  avatarSize: _avatarSize,
                  onPush: () async {
                    UserModel userModel = Provider.of<UserModel>(context, listen: false);
                    if (userModel.isLoggedIn) {
                      if (prefs?.getBool('isApiCallPending') == false) {
                        await prefs?.setBool('isApiCallPending', true);
                      }
                      print("api call before on tab  get value ====> ${prefs?.get('isApiCallPending')}");
                      _startPress();
                    } else {
                      HapticFeedback.lightImpact();
                      Navigator.pushNamed(context, RouteName.Login);
                    }
                  },
                  onRelease: () {
                                          print("connect disconnect");

                    _stopPress();
                  },
                ),
              ],
            );
          }),
        ),
      ),
    );
  }

  // Lower widgets:

  Widget _getLower() {
    //print("put down");
    UserModel userModel = Provider.of<UserModel>(context, listen: true);
    return Padding(
      padding: (userModel.data.premiumName == planName && Platform.isIOS)
          ? const EdgeInsets.only(bottom: 25)
          : const EdgeInsets.all(0),
      child: Container(
        margin: EdgeInsets.only(top: 10),
        child: Selector<PartnerModel, bool>(
          selector: (
            _,
            partnerModel,
          ) =>
              partnerModel.isLinked,
          builder: (_, isLinked, __) {
            if (!isLinked) {
              return Container(
                height: 120.0,
              );
            }

            return Selector<SyncModel, bool>(
              selector: (_, syncModel) => syncModel.synced,
              builder: (_, synced, __) {
                if (synced) {
                  print("Put down the phone");
                  return Container(
                    height: 120.0,
                    child: Stack(
                      children: <Widget>[
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Visibility(
                            visible: synced,
                            child: Lottie.asset('assets/lottie/hearts.json', animate: synced),
                          ),
                        ),
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Text(
                                'Put down the phone and get to it!',
                                style: TextStyle(
                                  fontSize: 15.0,
                                  color: Color(0xFFFE8385),
                                ),
                                textAlign: TextAlign.center,
                              ),
                              SizedBox(height: 8.0),
                              Text(
                                'Press and hold the LoveSync button to reset.',
                                style: TextStyle(
                                  fontSize: 14.0,
                                  color: colorTextAccent,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return Container(
                  height: 120.0,
                  padding: EdgeInsets.only(bottom: 24.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            'Until',
                            style: TextStyle(
                              fontSize: 14.0,
                              fontWeight: FontWeight.w500,
                              color: colorTextAccent,
                            ),
                          ),
                          SizedBox(height: 16.0),
                          Text(
                            _durationText,
                            style: TextStyle(
                              fontSize: 24.0,
                              color: colorText,
                            ),
                          ),
                        ],
                      ),
                      Visibility(
                        visible: !synced,
                        child: GestureDetector(
                          onTap: ()  {
                            print('hello');
                             _reset(true);
                          },
                          child: AnimatedOpacity(
                            opacity:
                                (_buttonSyncState == SyncState.PENDING || _buttonSyncState == SyncState.ON) ? 1.0 : 0.0,
                            duration: Duration(milliseconds: 300),
                            child: Container(
                              width: 70.0,
                              height: 70.0,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: colorBackground,
                                border: Border.all(
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  'Reset',
                                  style: TextStyle(
                                    fontSize: 12.0,
                                    fontWeight: FontWeight.w500,
                                    color: colorText,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      GestureDetector(
                          onTap: () async {
                            nudgeController.clear();
                            dropdownValue = null;
                            APIService service = locator<APIService>();
                            PartnerModel partnerModel = Provider.of<PartnerModel>(context, listen: false);
                            (userModel.data.premiumName == planName)
                                ? CommonDialog.commonAlertShowDialog(
                                    context: context,
                                    height: 45,
                                    width: 45,
                                    fontSize: 15,
                                    dialogText: "To enable private messaging with your partner",
                                    imagePath: 'assets/premium_icon.png',
                                    decorationImagePath: 'assets/yellow_circle.png',
                                    onPressed: () {
                                      Navigator.pushNamed(context, RouteName.SubscriptionPlan);
                                    },
                                    titleText: "Upgrade to LoveSync+",
                                    buttonText: "Upgrade")
                                : await showDialog(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return AlertDialog(
                                        //insetPadding: EdgeInsets.zero,
                                        shape:
                                            RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(15))),
                                        backgroundColor: Colors.white,
                                        title: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              'To ${partnerModel.data?.username}',
                                              style: TextStyle(
                                                  fontSize: 18, fontWeight: FontWeight.w600, color: Colors.black),
                                            ),
                                            GestureDetector(
                                                onTap: () {
                                                  Navigator.of(context).pop();
                                                  print('clear on cancel ====>');
                                                  setState(() {
                                                    nudgeController.clear();
                                                    dropdownValue = null;
                                                    _key.currentState?.reset();
                                                  });
                                                },
                                                child: Icon(
                                                  Icons.cancel,
                                                  color: Colors.grey,
                                                ))
                                          ],
                                        ),
                                        content: SingleChildScrollView(
                                          child: StatefulBuilder(
                                            builder: (context, setState) {
                                              return Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  ButtonTheme(
                                                    alignedDropdown: true,
                                                    child: DropdownButtonFormField(
                                                      key: _key,
                                                      value: dropdownValue,
                                                      isExpanded: true,
                                                      validator: (value) {
                                                        if (value == null) {
                                                          return 'Select a Message';
                                                        }
                                                        return null;
                                                      },
                                                      items: nudge.map<DropdownMenuItem<String>>((value) {
                                                        return DropdownMenuItem<String>(
                                                          value: value,
                                                          child: Row(
                                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                            children: [
                                                              Flexible(
                                                                child: Text(
                                                                  value,
                                                                  overflow: TextOverflow.ellipsis,
                                                                  style: TextStyle(
                                                                      color: Colors.black, fontWeight: FontWeight.w400),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        );
                                                      }).toList(),
                                                      onChanged: (String? newValue) {
                                                        setState(() {
                                                          isSelectNudge = true;
                                                          // ButtonScreen.isMessage = true;
                                                          dropdownValue = newValue;
                                                          ButtonScreen.drpDownValues = dropdownValue ?? '';

                                                          nudgeController.clear();
                                                        });
                                                      },
                                                      decoration: InputDecoration(
                                                        border: const OutlineInputBorder(),
                                                        hintText: 'Select a message',
                                                        contentPadding: EdgeInsets.symmetric(
                                                          horizontal: 6.0,
                                                          vertical: 3.0,
                                                        ),
                                                        hintStyle: const TextStyle(
                                                            fontWeight: FontWeight.w500, color: Colors.grey),
                                                      ),
                                                    ),
                                                  ),
                                                  //SizedBox(height: 5,),
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      Expanded(
                                                        child: Divider(
                                                          color: Colors.grey,
                                                          thickness: 1,
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding: const EdgeInsets.all(18.0),
                                                        child: Text(
                                                          'Or',
                                                          style: TextStyle(
                                                              color: Colors.black, fontWeight: FontWeight.w500),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: Divider(
                                                          color: Colors.grey,
                                                          thickness: 1,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text(
                                                        'Custom Message',
                                                        style: TextStyle(
                                                            fontSize: 18,
                                                            fontWeight: FontWeight.w600,
                                                            color: Colors.black),
                                                      ),
                                                      SizedBox(
                                                        height: 10,
                                                      ),
                                                      TextFormField(
                                                        controller: nudgeController,
                                                        maxLines: 4,
                                                        keyboardType: TextInputType.text,
                                                        onTap: () {},
                                                        onChanged: (value) {
                                                          if (ButtonScreen.drpDownValues != 'Select a message') {
                                                            setState(() {
                                                              ButtonScreen.customMessage = nudgeController.text;
                                                              // _key.currentState?.reset();
                                                              dropdownValue = null;
                                                            });
                                                          }
                                                        },
                                                        style: TextStyle(color: Colors.black, fontSize: 17),
                                                        decoration: InputDecoration(
                                                            //enabled: false,
                                                            border: OutlineInputBorder(
                                                                borderSide: BorderSide(
                                                                  color: Colors.grey,
                                                                ),
                                                                borderRadius: BorderRadius.circular(10)),
                                                            enabledBorder: OutlineInputBorder(
                                                                borderSide: BorderSide(
                                                                  color: Colors.grey,
                                                                ),
                                                                borderRadius: BorderRadius.circular(10)),
                                                            focusedBorder: OutlineInputBorder(
                                                                borderSide: BorderSide(color: Colors.grey),
                                                                borderRadius: BorderRadius.circular(10)),
                                                            hintText: "Write Message Here...",
                                                            hintStyle: TextStyle(
                                                              color: Colors.grey,
                                                              fontSize: 17,
                                                              fontWeight: FontWeight.w500,
                                                            )),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(
                                                    height: 15,
                                                  ),
                                                  Container(
                                                    height: 45,
                                                    width: double.infinity,
                                                    child: Material(
                                                      shape: RoundedRectangleBorder(
                                                          borderRadius: BorderRadius.circular(5)),
                                                      color: (dropdownValue != null || nudgeController.text.isNotEmpty)
                                                          ? Color(0xFFFF7D80)
                                                          : Color(0xFFFF7D80).withOpacity(0.6),
                                                      child: InkWell(
                                                        highlightColor: Colors.white.withOpacity(0.2),
                                                        onTap:
                                                            (dropdownValue != null || nudgeController.text.isNotEmpty)
                                                                ? () async {
                                                                    setState(() {
                                                                      isLoading = true;
                                                                    });
                                                                    ButtonScreen.customMessage =
                                                                        nudgeController.text.trim();
                                                                    print(
                                                                        "ButtonScreen ========> ${ButtonScreen.customMessage}");
                                                                    await service.request(SendMessageRequest(
                                                                        (ButtonScreen.customMessage.isNotEmpty)
                                                                            ? ButtonScreen.customMessage
                                                                            : dropdownValue.toString(),
                                                                        partnerModel.data!.id));
                                                                    setState(() {
                                                                      isLoading = false;
                                                                    });
                                                                    Navigator.of(context).pop();
                                                                    nudgeController.clear();
                                                                    dropdownValue = null;
                                                                    _key.currentState?.reset();
                                                                  }
                                                                : null,
                                                        child: Center(
                                                            child: isLoading
                                                                ? CupertinoActivityIndicator(
                                                                    animating: true,
                                                                    color: Colors.white,
                                                                  )
                                                                : Text(
                                                                    "Send",
                                                                    style: TextStyle(
                                                                        fontSize: 17,
                                                                        color: Colors.white,
                                                                        fontWeight: FontWeight.w500),
                                                                  )),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              );
                                            },
                                          ),
                                        ),
                                      );
                                    });
                          },
                          child: Container(
                            height: 50,
                            width: 50,
                            child: Image.asset(
                              'assets/chat-icon.png',
                              fit: BoxFit.cover,
                              color: userModel.data.premiumName == planName ? Colors.grey : null,
                            ),
                          )),
                    ],
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  // Handlers:

  void _updateInvitation(bool accept) async {
    UserModel userModel = Provider.of<UserModel>(context, listen: false);
    PartnerModel partnerModel = Provider.of<PartnerModel>(context, listen: false);
    APIService service = locator<APIService>();

    try {
      final response = accept ? UpdateInviteResponse.Accept : UpdateInviteResponse.Reject;
      await service.request(UpdateInviteRequest(partnerModel.data?.id ?? -1, response));

      if (accept) {
        print("error =====>>>===");
        Analytics analytics = locator<Analytics>();
        analytics.partnerConnected(userModel.data.id, partnerModel.data?.id, true);
        await partnerModel.setState(PartnerModelState.linked);
      } else {
        print("error_widget =====>>>");
        await partnerModel.setState(null);
      }
    } catch (e) {
      print("error =====>>>");
      print(e);
    }
  }

  void _moveAvatarHeader() {
    UserModel userModel = Provider.of<UserModel>(context, listen: false);
    RenderBox? targetBox = _avatarSecondaryPosition.currentContext?.findRenderObject() as RenderBox;
    Offset targetPosition = targetBox.localToGlobal(Offset.zero);
    RenderBox syncBox = _sync.currentContext?.findRenderObject() as RenderBox;
    Offset syncPosition = syncBox.localToGlobal(Offset.zero);
    double dx1 = 2 * (targetPosition.dx - syncPosition.dx) / (syncBox.size.width - 60.0) - 1.0;
    double dx = 2 * (targetPosition.dx - syncPosition.dx) / (syncBox.size.width - 80.0) - 1.0;
    double dy = 2 * (targetPosition.dy - syncPosition.dy) / (syncBox.size.height - 80.0) - 1.0;
    double dy1 = 2 * (targetPosition.dy - syncPosition.dy) / (syncBox.size.height - 60.0) - 1.0;
    print("dy ===>  $dy");
    print("dx ===>  $dx");
    if (!dx.isNaN && !dy.isNaN) {
      globalDx = (userModel.data.premiumName == planName) ? dx1 : dx;
      globalDy = userModel.data.premiumName == planName ? dy1 : dy;
    }
    print("globalDy $globalDy");
    print("globalDx $globalDx");

    setState(() {
      _avatarSize = 80.0;
      _avatarDx = (!dx.isNaN)
          ? userModel.data.premiumName == planName
              ? dx1
              : dx
          : globalDx;
      print("dy ===> $dy");
      _avatarDy = (!dy.isNaN)
          ? userModel.data.premiumName == planName
              ? dy1
              : dy
          : globalDy;
    });

    print("dx ========> $_avatarDx");
    print("dx ========> $_avatarDy");
    print("dx ========> $_avatarSize");
  }

  void _moveAvatarButton() {
    setState(() {
      _avatarDuration = Duration(milliseconds: 400);
      _avatarSize = null;
      _avatarDx = 0.0;
      _avatarDy = 0.0;
    });
  }

  Future<void> _startPress() async {
    UserModel userModel = Provider.of<UserModel>(context, listen: false);
    if (_syncInteractLock || _syncDown) {
      return;
    }

    _syncDown = true;

    if (syncConfirmTimer != null) {
      print("Timer cancel =====>");
      syncConfirmTimer?.cancel();
      syncConfirmTimer = null;
    }

    PartnerModel partnerModel = Provider.of<PartnerModel>(context, listen: false);
    SyncModel syncModel = Provider.of<SyncModel>(context, listen: false);
    final downAt = DateTime.now().millisecondsSinceEpoch;
    _buttonDownAt = downAt;

    if (_buttonSyncState == SyncState.ON) {
      Timer(Duration(seconds: 2), () {
        if (_buttonDownAt == downAt) {
          _reset(false);
        }
      });
    }

    if (_buttonSyncState != SyncState.OFF && _buttonSyncState != SyncState.PENDING) {
      return;
    }

    if (syncModel.synced) {
      return;
    }

    // if (_minutes > 150) {
    //   print("dwedwdwded");
    //   return;
    // }

    HapticFeedback.lightImpact();

    if (!partnerModel.isLinked && !partnerModel.isInvited && !partnerModel.isRequesting) {
      Navigator.pushReplacementNamed(context, RouteName.LinkPartner);
      return;
    }

    var hasPartner = partnerModel.isLinked;
    if (hasPartner) {
      if (_minutes < 15) {
        DateTime expiry = DateTime.now().add(Duration(minutes: 15));
        setState(() {
          _buttonSyncState = SyncState.INTERACT;
          _buttonTimeText = '15m';
          _durationText = _getExpiryString(expiry);
          _minutes = 15;
          // print("duration Text ======> $ _durationText");
        });
      } else if (_minutes < 60) {
        DateTime expiry = DateTime.now().add(Duration(minutes: 60));
        setState(() {
          _buttonSyncState = SyncState.INTERACT;
          _buttonTimeText = '1h';
          _durationText = _getExpiryString(expiry);
          _minutes = 60;
        });
      } else if (_minutes < 300) {
        DateTime expiry = DateTime.now().add(Duration(minutes: 300));
        setState(() {
          _buttonSyncState = SyncState.INTERACT;
          _buttonTimeText = '5h';
          _durationText = _getExpiryString(expiry);
          _minutes = 300;
        });
      } else {
        DateTime expiry;
        userModel.data.premiumName == planName
            ? expiry = DateTime.now().add(Duration(minutes: 480))
            : expiry = DateTime.now().add(Duration(minutes: 720));
        userModel.data.premiumName == planName
            ? setState(() {
                _buttonSyncState = SyncState.INTERACT;
                _buttonTimeText = '5h';
                _durationText = _getExpiryString(expiry);
                _minutes = 300;
                //  print("duration Text ======> $ _durationText");
                WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                  CommonDialog.commonAlertShowDialog(
                      context: context,
                      height: 45,
                      width: 45,
                      fontSize: 15,
                      dialogText: "To enter a sync time longer than 5 hours, upgrade to LoveSync+",
                      imagePath: 'assets/premium_icon.png',
                      decorationImagePath: 'assets/yellow_circle.png',
                      onPressed: () {
                        Navigator.pushNamed(context, RouteName.SubscriptionPlan);
                      },
                      titleText: "Upgrade to LoveSync+",
                      buttonText: "Upgrade");
                });
              })
            : setState(() {
                _buttonSyncState = SyncState.INTERACT;
                _buttonTimeText = '12h';
                _durationText = _getExpiryString(expiry);
                _minutes = 720;
                //  print("duration Text ======> $ _durationText");
              });
      }
      _timer = interval(Duration(milliseconds: 1300), (timer) {
        _timer = timer;

        switch (_minutes) {
          case 15:
            HapticFeedback.mediumImpact();
            DateTime expiry = DateTime.now().add(Duration(minutes: 60));
            setState(() {
              // print("duration Text ======> $ _durationText");
              _buttonTimeText = '1h';
              _durationText = _getExpiryString(expiry);
              _minutes = 60;
            });
            break;

          case 60:
            HapticFeedback.heavyImpact();
            DateTime expiry = DateTime.now().add(Duration(minutes: 300));
            setState(() {
              print("5h ======> $_minutes");
              _buttonTimeText = '5h';
              _durationText = _getExpiryString(expiry);
              _minutes = 300;
            });
            break;

          case 300:
            HapticFeedback.heavyImpact();
            DateTime expiry;
            userModel.data.premiumName == planName
                ? expiry = DateTime.now().add(Duration(minutes: 720))
                : expiry = DateTime.now().add(Duration(minutes: 720));
            userModel.data.premiumName == planName
                ? setState(() {
                    _buttonSyncState = SyncState.INTERACT;
                    _buttonTimeText = '5h';
                    _durationText = _getExpiryString(expiry);
                    _minutes = 300;
                    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                      CommonDialog.commonAlertShowDialog(
                          context: context,
                          height: 45,
                          width: 45,
                          fontSize: 15,
                          dialogText: "To enter a sync time longer than 5 hours, upgrade to LoveSync+",
                          imagePath: 'assets/premium_icon.png',
                          decorationImagePath: 'assets/yellow_circle.png',
                          onPressed: () {
                            Navigator.pushNamed(context, RouteName.SubscriptionPlan);
                          },
                          titleText: "Upgrade to LoveSync+",
                          buttonText: "Upgrade");
                      // showDialog(
                      //     context: context,
                      //     builder: (BuildContext context) {
                      //       return AlertDialog(
                      //         shape: RoundedRectangleBorder(
                      //             borderRadius:
                      //                 BorderRadius.all(Radius.circular(15))),
                      //         contentPadding:
                      //             EdgeInsets.only(top: 12.0, bottom: 10.0),
                      //         backgroundColor: Colors.white,
                      //         title: Column(
                      //           children: [
                      //             Row(
                      //               mainAxisAlignment: MainAxisAlignment.end,
                      //               children: [
                      //                 GestureDetector(
                      //                     onTap: () {
                      //                       Navigator.of(context).pop();
                      //                     },
                      //                     child: Icon(
                      //                       Icons.cancel,
                      //                       color: Colors.grey,
                      //                     )),
                      //               ],
                      //             ),
                      //             Container(
                      //               height: 80,
                      //               width: 80,
                      //               decoration: BoxDecoration(
                      //                   image: DecorationImage(
                      //                       image: AssetImage(
                      //                           'assets/yellow_circle.png'))),
                      //               child: Center(
                      //                   child: Image.asset(
                      //                 'assets/premium_icon.png',
                      //                 height: 45,
                      //                 width: 45,
                      //               )),
                      //             ),
                      //             SizedBox(
                      //               height: 15,
                      //             ),
                      //             Center(
                      //               child: Text(
                      //                 'Upgrade to LoveSync+',
                      //                 style: TextStyle(
                      //                     fontSize: 20,
                      //                     fontWeight: FontWeight.bold,
                      //                     color: Colors.black),
                      //               ),
                      //             ),
                      //           ],
                      //         ),
                      //         content: SizedBox(
                      //           width: MediaQuery.of(context).size.width,
                      //           child: Padding(
                      //               padding: const EdgeInsets.symmetric(
                      //                   horizontal: 30),
                      //               child: Column(
                      //                 mainAxisSize: MainAxisSize.min,
                      //                 children: [
                      //                   Center(
                      //                       child: Text(
                      //                     "To enter a sync time longer than 5 hours, upgrade to LoveSync+",
                      //                     style: TextStyle(
                      //                         color: Colors.black54,
                      //                         fontWeight: FontWeight.w700,
                      //                         fontSize: 15),
                      //                   )),
                      //                 ],
                      //               )),
                      //         ),
                      //         actions: [
                      //           Padding(
                      //             padding: const EdgeInsets.symmetric(
                      //                 horizontal: 17),
                      //             child: CommonButton(
                      //                 onpressed: () {
                      //                   Navigator.pushNamed(context,
                      //                       RouteName.SubscriptionPlan);
                      //                 },
                      //                 name: 'Upgrade'),
                      //           ),
                      //           SizedBox(
                      //             height: 10,
                      //           ),
                      //         ],
                      //       );
                      //     });
                    });
                  })
                : setState(() {
                    _buttonTimeText = '12h';
                    _durationText = _getExpiryString(expiry);
                    _minutes = 720;
                  });

            break;

          case 720:
            _timer?.cancel();
            _timer = null;
            break;
        }
      });

      _fractionAnimator = AnimationController(
        vsync: this,
        duration: Duration(milliseconds: (3900 * (1.0 - _buttonFraction)).toInt()),
      );
      var animation = Tween(begin: _buttonFraction, end: 1.0).animate(_fractionAnimator!);
      animation.addListener(() {
        setState(() {
          _buttonFraction = animation.value;
        });
      });
      _fractionAnimator?.forward();
    }
  }

  Future<void> _stopPress() async {
    print("Timer call start =====>asasassasa");
    if (!_syncDown) {
      return;
    }

    _syncDown = false;

    _buttonDownAt = -1;
    if (_buttonSyncState != SyncState.INTERACT) {
      return;
    }

    setState(() {
      _buttonSyncState = SyncState.PENDING;
    });

    _performSync();

    if (syncConfirmTimer != null) {
      syncConfirmTimer?.cancel();
      syncConfirmTimer = null;
    }
    print("Timer call start =====>");
    syncConfirmTimer = Timer(Duration(seconds: 4), () async {
      SyncModel syncModel = Provider.of<SyncModel>(context, listen: false);

      final service = locator<APIService>();
      UserModel userModel = Provider.of<UserModel>(context, listen: false);
      print("minutes Timer call =====>${syncModel.sentActive?.minutes}");
      //   await _performSync().then((value) async {
      ///after issue resolves
      final UserResponse userResponse = await service.request(SyncScoreRequest());
      await userModel.setData(
        userResponse,
      );

      print("Timer call =====>${syncModel.sentActive}");
      if (syncModel.sentActive != null) {
        _performSyncStart(syncModel.sentActive!.id);
      }
      // });
    });

    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }

    if (_fractionAnimator != null) {
      _fractionAnimator?.stop();
      _fractionAnimator = null;
    }

    _correctButtonFraction();
  }

  void _correctButtonFraction() {
    var correctionAnimator = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
    );

    var targetFraction = 0.0;
    switch (_minutes) {
      case 15:
        targetFraction = 0.25;
        break;
      case 60:
        targetFraction = 0.50;
        break;
      case 300:
        targetFraction = 0.75;
        break;
      case 480:
        targetFraction = 1.0;
        break;
      case 720:
        targetFraction = 1.0;
        break;
    }

    var begin = _buttonFraction;
    var end = targetFraction;

    var animation = Tween(begin: begin, end: end).animate(correctionAnimator);
    animation.addListener(() {
      setState(() {
        _buttonFraction = animation.value;
      });
    });
    correctionAnimator.forward();
  }



  _performSync() async {
    _syncInteractLock = true;

    final service = locator<APIService>();

    final userModel = Provider.of<UserModel>(context, listen: false);
    final partnerModel = Provider.of<PartnerModel>(context, listen: false);
    final syncModel = Provider.of<SyncModel>(context, listen: false);
//
    try {
      if (!partnerModel.isLinked) throw ("Partner not linked"); //
     
      //  final SyncResponse syncResponse = await service.request(SyncRequest(partnerModel.data?.id ?? -1, 1, true));
      int id = partnerModel.data?.id ?? -1;
     
      ButtonScreen.count = ButtonScreen.idCounts[id] ?? 1;
    

      final SyncResponse syncResponse =
          await service.request(SyncRequest(partnerModel.data?.id ?? -1, _minutes, true, ButtonScreen.count));

          //   final SyncResponse syncResponse =
          // await service.request(SyncRequest(partnerModel.data?.id ?? -1, 2, true, ButtonScreen.count));
      ButtonScreen.idCounts[id] = ButtonScreen.count + 1;
      print('-----count ${ButtonScreen.count}');
      print('-----count ${ButtonScreen.idCounts}');
      print('-----count $id');
      await syncModel.setSentActive(syncResponse, userModel.data.id);

      
    } catch (err) {
      isPlanExpire = true;
      setState(() {
        _minutes = 0;
        _buttonFraction = 0.0;
        _buttonSyncState = SyncState.OFF;
        _errorMessage = err.toString();
        _durationText = '...';
        print("error ======> $_errorMessage");
      });

      await showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(15))),
              contentPadding: EdgeInsets.only(top: 12.0, bottom: 10.0),
              backgroundColor: Colors.white,
              title: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                            Future.delayed(Duration(microseconds: 200))..then((value) => _durationText = '...');
                          },
                          child: Icon(
                            Icons.cancel,
                            color: Colors.grey,
                          )),
                    ],
                  ),
                  Container(
                    height: 80,
                    width: 80,
                    decoration: BoxDecoration(image: DecorationImage(image: AssetImage('assets/yellow_circle.png'))),
                    child: Center(
                        child: Image.asset(
                      'assets/premium_icon.png',
                      height: 45,
                      width: 45,
                    )),
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Center(
                    child: Text(
                      'You have used your 3 free syncs this month',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black),
                    ),
                  ),
                ],
              ),
              content: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Center(
                            child: Text(
                          "You will receive another 3 syncs next month.",
                          style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w700, fontSize: 15),
                        )),
                        SizedBox(
                          height: 15,
                        ),
                        Center(
                            child: Text(
                          "For Unlimited Syncs and other great benefits Upgrade to LoveSync+",
                          style: TextStyle(
                              color: Colors.black.withOpacity(0.8), fontWeight: FontWeight.w500, fontSize: 16),
                        )),
                      ],
                    )),
              ),
              actions: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 17),
                  child: CommonButton(
                      onPressed: () {
                        Navigator.pushNamed(context, RouteName.SubscriptionPlan).then((value) => _durationText = '...');
                      },
                      name: 'Upgrade'),
                ),
                SizedBox(
                  height: 10,
                ),
              ],
            );
          });

      print(err);
    } finally {
      _syncInteractLock = false;
    }
  }

  void _performSyncStart(int id) async {
    _syncInteractLock = true;

    final service = locator<APIService>();
    final userModel = Provider.of<UserModel>(context, listen: false);
    final syncModel = Provider.of<SyncModel>(context, listen: false);

    try {
      if (syncModel.exists) {
        print("sync completed");

        print("after api call get value ====> ${prefs?.get('isApiCallPending')}");
        final SyncResponse syncResponse = await service.request(StartSyncRequest(id));
        await prefs?.setBool('isApiCallPending', false);
        // if (syncResponse.minutes != "0") {
        await syncModel.setSentActive(syncResponse, userModel.data.id);
        //}
        Analytics analytics = locator<Analytics>();
        analytics.syncInput(userModel.data.id, syncResponse.id);

        setState(() {
          _buttonSyncState = SyncState.ON;
          _durationText = _getExpiryString(syncModel.sentActive!.expiresAt.toLocal());
          _headerPaddingLeft = 0.0;
        });
        _moveAvatarButton();
      }
    } catch (err) {
      print('err');
      print(err);
      await syncModel.setSentActive(null, -1);
    } finally {
      _syncInteractLock = false;
    }
  }

   _reset(bool? isExpire) async {
    // print("DeleteSyncRequest1 -----_avatarSize ${_avatarSize}");
    // print("DeleteSyncRequest1 -----_avatarSize ${isExpire}");
    final partnerModel = Provider.of<PartnerModel>(context, listen: false);
    isExpire = _avatarSize == null ? false : isExpire;
    print('---reset$isExpire');

    if (_syncInteractLock) {
      return;
    }

    if (_buttonSyncState == SyncState.ON || _buttonSyncState == SyncState.PENDING) {
      HapticFeedback.lightImpact();

      setState(() {
        _minutes = 0;
        _buttonFraction = 0.0;
        _buttonSyncState = SyncState.OFF;
        _durationText = '...';
        _headerPaddingLeft = 96.0;
      });

      _moveAvatarHeader();
      await _performReset(isExpire);
     

      print('resetsasasasasaasas aaaaa$_avatarSize');
      print('resetsasasasasaasas aaaaa}');
      
      //_getLower();
    }
  }

  Future<void> _performReset(bool? isExpire) async {
    await prefs?.setBool('isApiCallPending', false);
    _syncInteractLock = true;

    final service = locator<APIService>();

    final partnerModel = Provider.of<PartnerModel>(context, listen: false);
    final syncModel = Provider.of<SyncModel>(context, listen: false);
    final userModel = Provider.of<UserModel>(context, listen: false);

    try {
      //print("partner linked ===> ${partnerModel.isLinked}");
      if (!partnerModel.isLinked) throw ('Partner not linked');
      // print("partner linked ===> ${partnerModel.isLinked}");
      // print("id============> DeleteSyncRequest1");
      // print("DeleteSyncRequest1 -----_avatarSize ${_avatarSize}");
      print('resetsasasasasaasas sentActive aaaaa}${syncModel.sentActive!.id}');
      print("minutes Timer call =====>${syncModel.sentActive?.minutes}");

      await service.request(DeleteSyncRequest(syncModel.sentActive!.id,
          isExpire: isExpire == true ? true : false, waitTime: syncModel.sentActive?.minutes ?? '0'));
      // await service.request(DeleteSyncRequest(syncModel.sentActive!.id, isExpire: false));
      print("id============> DeleteSyncRequest24${syncModel.sentActive}");
      if (syncModel.synced) {
        print("id============> DeleteSyncRequest3");
        final SyncResponse response = await service.request(GetSyncRequest(syncModel.sentActive!.id));
        print("id============>${response.syncedId}");
        await service.request(DeleteSyncRequest(response.syncedId, waitTime: syncModel.sentActive?.minutes ?? '0'));
      }
      await syncModel.setSentActive(null, -1);
    
         ButtonScreen.idCounts = {};
      
      
      print("reste data ${ButtonScreen.idCounts}");
    } catch (err) {
      print(err);
    } finally {
      _syncInteractLock = false;
    }
  }

  void _startColorAnimation() {
    print("change animation =======>");
    _colorChangeTimer = interval(Duration(milliseconds: 1300), (timer) {
      _colorChangeTimer = timer;

      if (_glowColor == colorAccent) {
        if (mounted) {
          setState(() {
            _glowColor = colorPrimary;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _glowColor = colorAccent;
          });
        }
      }
    });
  }

  void _stopColorAnimation() {
    print('-------_colorChangeTimer _syncSent${_colorChangeTimer}');

    if (_colorChangeTimer != null) {
      _colorChangeTimer?.cancel();
      _colorChangeTimer = null;
    }

    setState(() {
      _glowColor = colorAccent;
    });
  }

  void _showTutorial() {
    tutorialCoachMark = TutorialCoachMark(
        targets: targets,
        colorShadow: Colors.red,
        textSkip: 'DONE',
        paddingFocus: 10,
        opacityShadow: 0.8,
        onFinish: () {})
      ..show(context: context);
  }

  // Utils:

  String _getExpiryString(DateTime expiry) {
    var formatter = new DateFormat('jm');
    return formatter.format(expiry);
  }

  Timer interval(Duration duration, func) {
    Timer function() {
      Timer timer = Timer(duration, function);
      func(timer);
      return timer;
    }

    return Timer(duration, function);
  }
}
