import 'dart:convert';
import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:lovesync/api/requests/change_avatar.dart';
import 'package:lovesync/api/responses/user.dart';
import 'package:lovesync/api/service.dart';
import 'package:lovesync/colors.dart';
import 'package:lovesync/data/model/partner.dart';
import 'package:lovesync/data/model/user.dart';
import 'package:lovesync/locator.dart';
import 'package:lovesync/router.dart';
import 'package:lovesync/ui/widget/avatar.dart';
import 'package:lovesync/ui/widget/button.dart';
import 'package:lovesync/ui/widget/header.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../api/requests/subscription_detail.dart';
import '../../purchase_setup/purchase_api.dart';
import '../widget/common_button.dart';

class SettingsScreen extends StatefulWidget {
  final VoidCallback onSignOut;
  final VoidCallback onDeleteAccount;
  final VoidCallback onUnlink;

  SettingsScreen({Key? key, required this.onSignOut, required this.onUnlink, required this.onDeleteAccount})
      : super(key: key);

  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with AutomaticKeepAliveClientMixin<SettingsScreen> {
  PackageInfo? packageInfo;

  @override
  bool get wantKeepAlive => true;

  bool isSyncScore = false;
  bool isPremium = false;
  String subSubscriptionDate = '';
  String premiumDurationType = '';

  @override
  void initState() {
    super.initState();

    PackageInfo.fromPlatform().then((value) {
      setState(() {
        packageInfo = value;
      });
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      APIService service = locator<APIService>();
      final userModel = Provider.of<UserModel>(context, listen: false);
      final UserResponse userResponse = await service.request(SubscriptionDetailRequest());
      //final UserResponse userResponse = await service.request(SubscriptionDetailRequest());
      await userModel.setData(userResponse);


    });
  }



  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      child: ListView(
        padding: EdgeInsets.fromLTRB(30.0, 36.0, 30.0, 200.0),
        physics: ClampingScrollPhysics(),
        shrinkWrap: true,
        children: <Widget>[
          Header('Settings'),
          _renderPersonalInfo(),
          SizedBox(height: 20.0),
          _renderPreferences(),
        ],
      ),
    );
  }

  Widget _renderLink(String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 50.0,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFF5B646C),
              width: 1.0,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Text(
              label,
              style: TextStyle(
                color: colorText,
                fontSize: 15.0,
              ),
            ),
            Image.asset('assets/icons/chevron.png'),
          ],
        ),
      ),
    );
  }

  Widget _renderData(String label, String value) {
    return Container(
      height: 50.0,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Color(0xFF5B646C),
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            label,
            style: TextStyle(
              color: colorText,
              fontSize: 15.0,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: colorText,
              fontWeight: FontWeight.w500,
              fontSize: 15.0,
            ),
          ),
        ],
      ),
    );
  }

  Widget _renderSectionLabel(String label) {
    return Container(
      margin: EdgeInsets.only(top: 32.0, bottom: 10.0),
      child: Text(
        label,
        style: TextStyle(fontWeight: FontWeight.w500, color: Color(0xFF9A9FA6)),
      ),
    );
  }

  Widget _renderPersonalInfo() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    //print("premiumname ====> ${userModel.data.premiumname}");
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        _renderSectionLabel('Personal Info'),
        _renderPerson(),
        Selector<UserModel, String>(
          selector: (_, userModel) {
            if (userModel.data == null) {
              return '';
            }
            return userModel.data.email ?? '';
          },
          builder: (_, email, __) => _renderData('My e-mail', email),
        ),
        _renderLink('Change Display Name', () {
          Navigator.pushNamed(context, RouteName.ChangeUsername);
        }),
        _renderLink('Change Password', () async {
          Navigator.pushNamed(context, RouteName.ChangePassword);
        }),
        userModel.data.premiumName == planName
            ? GestureDetector(
                onTap: () {
                  Navigator.pushNamed(context, RouteName.SubscriptionPlan);
                },
                child: FittedBox(
                  child: Container(
                    height: 85,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                        border: Border.all(color: Color(0xFFFF7D80)), borderRadius: BorderRadius.circular(5)),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            SizedBox(
                              width: 5,
                            ),
                            Image.asset(
                              'assets/premium_icon.png',
                              height: 45,
                              width: 45,
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            Flexible(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Upgrade to LoveSync+",
                                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 16),
                                  ),
                                  Text(
                                    "Enjoy all benefits without restrictions",
                                    style: TextStyle(
                                        height: 1.5,
                                        color: Colors.grey.shade500,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            Image.asset('assets/icons/chevron.png'),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              )
            : _renderLink('LoveSync+', () async {
                String startDateString = userModel.data.subSubscriptionDate ?? '';
                subSubscriptionDate = DateFormat("dd MMM yyyy")
                    .format(DateTime.parse(userModel.data.subSubscriptionDate ?? DateTime.now().toString()));
                print("object: ${userModel.data.resetDate}");
                print("object: ${userModel.data.startDate}");
                String dateString = "09 Nov 2023";
                DateFormat format = DateFormat("dd MMM yyyy");
                DateTime dateTime = format.parse(dateString);

                print(dateTime);

          DateTime expiryDateTime = DateTime.parse(userModel.data.subSubscriptionDate ?? DateTime.now().toString());

          DateTime purchaseDate = DateTime.parse(userModel.data.resetDate ?? DateTime.now().toString());

          // Calculate the difference between expiry date and current date
          int difference = expiryDateTime.difference(purchaseDate).inDays;


                // Duration difference = expiryDateTime.difference();
                if (difference >= 31) {
                  premiumDurationType = "year";
                  print("User has purchased a one-year Premium plan.");
                } else {
                  premiumDurationType = "month";
                  print("User has purchased a one-month Premium plan.");
                }
                // 2023-10-10T06:26:09.870Z

                // premiumDurationType =
                // premiumLength = userModel.data.
                print("startDateString ${userModel.data.subSubscriptionDate}");
                // DateTime startDate = DateTime.parse(startDateString);
                // SubScriptionDate = DateFormat('dd MMM yyyy').format(startDate) ?? '';
                await showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(15))),
                        contentPadding: EdgeInsets.only(top: 12.0, bottom: 10.0),
                        backgroundColor: Colors.white,
                        title: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                GestureDetector(
                                    onTap: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: Icon(
                                      Icons.cancel,
                                      color: Colors.grey,
                                    )),
                              ],
                            ),
                            Container(
                              height: 80,
                              width: 80,
                              decoration:
                                  BoxDecoration(image: DecorationImage(image: AssetImage('assets/yellow_circle.png'))),
                              child: Center(
                                  child: Image.asset(
                                'assets/premium_icon.png',
                                height: 45,
                                width: 45,
                              )),
                            ),
                            SizedBox(
                              height: 15,
                            ),
                            FittedBox(
                              child: Column(
                                children: [
                                  Center(
                                    child: Text(
                                      'Activated Subscription',
                                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black),
                                    ),
                                  ),
                                  Center(
                                    child: Text(
                                      'Plan Details',
                                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                        content: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 30),
                              child: Column(
                                mainAxisSize: MainAxisSize.min, //
                                children: [
                                  Center(
                                      child: Text(
                                    "Access all the premium\nfeatures for one $premiumDurationType",
                                    style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w700, fontSize: 15),
                                  )),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  FittedBox(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Your subscription plan will expire on",
                                          style: TextStyle(
                                            color: Colors.black.withOpacity(0.8),
                                            fontWeight: FontWeight.w500,
                                            fontSize: 16,
                                          ),
                                          maxLines: 1,
                                        ),
                                        Text(
                                          "date $subSubscriptionDate",
                                          style: TextStyle(
                                            color: Colors.black.withOpacity(0.8),
                                            fontWeight: FontWeight.w500,
                                            fontSize: 16,
                                          ),
                                          maxLines: 1,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )),
                        ),
                        actions: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 17),
                            child: CommonButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                name: 'Continue'),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                        ],
                      );
                    });
              }),
        SizedBox(height: 20.0),
        Button(
          'Sign out',
          theme: Button.THEME_DEFAULT,
          onTap: this.widget.onSignOut,
        ),
        SizedBox(height: 12.0),
        Selector<PartnerModel, bool>(
          selector: (_, partnerModel) => partnerModel.isLinked,
          builder: (_, isLinked, __) => Visibility(
            visible: isLinked,
            child: Button(
              'Unlink Partner',
              theme: Button.THEME_NEGATIVE,
              onTap: this.widget.onUnlink,
            ),
          ),
        ),
      ],
    );
  }

  void _changeAvatar() async {
    final image = await ImagePicker().pickImage(
      maxWidth: 256,
      maxHeight: 256,
      source: ImageSource.gallery,
      preferredCameraDevice: CameraDevice.front,
    );

    if (image == null) return;

    final user = Provider.of<UserModel>(context, listen: false);
    Map<String, dynamic> userData = user.data.toJSON();
    userData['image'] = {'url': image.path};
    await user.setData(UserResponse.fromJSON(userData));

    final bytes = await image.readAsBytes();
    final base64 = "data:image/jpeg;base64," + base64Encode(bytes);

    final service = locator<APIService>();

    UserResponse newUser = await service.request(ChangeAvatarRequest(base64));
    //print("new image path ===> ${newUser.image}");

    await user.setData(newUser);
  }

  Widget _renderPerson() {
    return Container(
      height: 100.0,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Color(0xFF5B646C),
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        children: <Widget>[
          GestureDetector(
            onTap: _changeAvatar,
            child: Selector<UserModel, String>(
              selector: (_, userModel) {
                if (userModel.data == null) {
                  //print("user profile image ===> null ");
                  return 'null';
                }
                return userModel.data.image ?? '';
              },
              builder: (_, image, __) {
                //print("user profile image ===> ${image}");
                return Avatar(image: image, size: 60.0);
              },
            ),
          ),
          SizedBox(width: 20.0),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Selector<UserModel, String>(
                selector: (_, userModel) {
                  if (userModel.data == null) {
                    return '';
                  }
                  return userModel.data.username;
                },
                builder: (_, username, __) {
                  return Text(
                    username,
                    style: TextStyle(
                      color: colorText,
                      fontSize: 18.0,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _renderSectionFooter(String label) {
    return Container(
      margin: EdgeInsets.only(top: 12.0),
      child: Text(
        label,
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 12, color: Color(0xFF9A9FA6)),
      ),
    );
  }

  Widget _renderAppInfo() {
    if (packageInfo == null) return _renderSectionFooter("");

    return _renderSectionFooter("v${packageInfo?.version} (build ${packageInfo?.buildNumber})");
  }

  Widget _renderPreferences() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        _renderSectionLabel('Preferences'),
        _renderLink('Notifications', () {
          AppSettings.openAppSettings(type: AppSettingsType.notification);
        }),
        _renderSectionLabel('Help and About'),
        _renderLink('Help', () async {
          await launchUrl(Uri.parse('https://lovesync.com/app/help/'));
        }),
        _renderLink('Rate App', () async {
          if (Platform.isIOS) {
            await launchUrl(Uri.parse(
                'itms-apps://itunes.apple.com/us/app/lovesync-couples-intimacy-app/id1483221951?mt=8&action=write-review'));
          } else if (Platform.isAndroid) {
            await launchUrl(Uri.parse('https://play.google.com/store/apps/details?id=com.lovesync.lsa'));
          }
        }),
        _renderLink('Leave Feedback', () async {
          await launchUrl(Uri.parse('mailto:<EMAIL>?subject=LoveSync%20Feedback'));
        }),
        _renderLink('Terms and Conditions', () async {
          await launchUrl(Uri.parse('https://www.freeprivacypolicy.com/terms/view/57d4853aac851b2baba5c486557fc8ae'));
        }),
        _renderLink('Privacy Policy', () async {
          await launchUrl(Uri.parse('https://www.freeprivacypolicy.com/privacy/view/6672ab5134223f021414a55da8131c13'));
        }),
        SizedBox(height: 20.0),
        Button(
          'Delete Account',
          theme: Button.THEME_DEFAULT,
          onTap: this.widget.onDeleteAccount,
        ),
        SizedBox(height: 24),
        _renderSectionFooter("Copyright 2020 BelaVivo LLC All rights reserved"),
        _renderSectionFooter("Patent: US11397978B2"),
        _renderSectionFooter("BelaVivo LLC, PO BOX 111, Sharon Center, OH 44274, USA"),
        _renderAppInfo(),
      ],
    );
  }
}
