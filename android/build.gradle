// buildscript {
// //    ext.kotlin_version = '1.4.50'
//     ext.kotlin_version = '1.7.20'
//     repositories {

//         google()
//         jcenter()
// //        mavenCentral()
//     }

//     dependencies {
// //        comlasspath 'com.android.tools.build:gradle:3.5.0'
//         classpath 'com.android.tools.build:gradle:7.1.2'
//         classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//         classpath 'com.google.gms:google-services:4.3.3'
//     }
// }

allprojects {
    repositories {
        google()
        jcenter()
        // mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

subprojects { project ->
    if (project.plugins.hasPlugin("com.android.application") || project.plugins.hasPlugin("com.android.library")) {
        def android = project.extensions.findByName("android")
        if (android != null && android.hasProperty("namespace") && android.namespace == null) {
            android.namespace = project.group
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
