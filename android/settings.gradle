// include ':app'

// def flutterProjectRoot = rootProject.projectDir.parentFile.toPath()

// def plugins = new Properties()
// def pluginsFile = new File(flutterProjectRoot.toFile(), '.flutter-plugins')
// if (pluginsFile.exists()) {
//     pluginsFile.withReader('UTF-8') { reader -> plugins.load(reader) }
// }

// plugins.each { name, path ->
//     def pluginDirectory = flutterProjectRoot.resolve(path).resolve('android').toFile()
//     include ":$name"
//     project(":$name").projectDir = pluginDirectory
// }

pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }
    settings.ext.flutterSdkPath = flutterSdkPath()

    includeBuild("${settings.ext.flutterSdkPath}/packages/flutter_tools/gradle")

      repositories {
        google()
        // mavenCentral()
        gradlePluginPortal()
    }
}
    plugins {
            id "dev.flutter.flutter-plugin-loader" version "1.0.0"
            id "com.android.application" version '8.10.1' apply false
            id "org.jetbrains.kotlin.android" version "2.1.0" apply false
            id("com.google.gms.google-services") version "4.4.3" apply false
    }

include ":app"