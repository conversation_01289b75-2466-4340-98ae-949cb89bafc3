package com.lovesync.lsa;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.lovesync.lsa.liquidswipe.LiquidPager;

import java.util.ArrayList;

public class OnboardingActivity extends FragmentActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        FrameLayout container = new FrameLayout(this);

        LiquidPager liquidPager = new LiquidPager(this);
        liquidPager.setId(View.generateViewId());
        liquidPager.setAdapter(new OnboardingAdapter(getSupportFragmentManager()));

        container.addView(liquidPager);
        setContentView(container);
    }

    class OnboardingAdapter extends FragmentPagerAdapter {

        private int total = 3;
        private ArrayList<OnboardingFragment> data = new ArrayList<>(total);

        public OnboardingAdapter(FragmentManager fm) {
            super(fm);
            data.add(OnboardingFragment.create(R.layout.fragment_onboarding_1));
            data.add(OnboardingFragment.create(R.layout.fragment_onboarding_2));
            data.add(OnboardingFragment.create(R.layout.fragment_onboarding_3));
        }

        @Override
        public Fragment getItem(int position) {
            return data.get(position);
        }

        @Override
        public int getCount() {
            return total;
        }
    }

    public static class OnboardingFragment extends Fragment {

        static OnboardingFragment create(int layoutRes) {
            OnboardingFragment fragment = new OnboardingFragment();
            Bundle arguments = new Bundle();
            arguments.putInt("layoutRes", layoutRes);
            fragment.setArguments(arguments);
            return fragment;
        }

        private int layoutRes;


        @Override
        public void onCreate(@Nullable Bundle savedInstanceState) {
            super.onCreate(savedInstanceState);
            Bundle bundle = getArguments();
            if (bundle != null) {
                layoutRes = bundle.getInt("layoutRes");
            }
        }

        @Nullable
        @Override
        public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
            View view = inflater.inflate(layoutRes, container, false);
            view.findViewById(R.id.skip)
                    .setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            requireActivity().finish();
                        }
                    });
            return view;
        }

    }

}
