package com.lovesync.lsa;

import android.content.DialogInterface;
import android.content.Intent;
import android.view.WindowManager.LayoutParams;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugins.GeneratedPluginRegistrant;

public class MainActivity extends FlutterActivity {
    private static final String ONBOARDING_CHANNEL = "lovesync/onboarding";
    private static final String DIALOG_CHANNEL = "lovesync/dialog";

    private MethodChannel.Result onboardingResult;

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine);
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), ONBOARDING_CHANNEL)
                .setMethodCallHandler(new MethodChannel.MethodCallHandler() {

                    @Override
                    public void onMethodCall(MethodCall call, MethodChannel.Result result) {
                        if ("launch".equals(call.method)) {
                            onboardingResult = result;
                            Intent intent = new Intent(MainActivity.this, OnboardingActivity.class);
                            startActivityForResult(intent, 123);
                        }
                    }
                });

        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), DIALOG_CHANNEL)
                .setMethodCallHandler(new MethodChannel.MethodCallHandler() {
                    @Override
                    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                        if ("dialog.alert".equals(call.method)) {
                            AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this)
                                    .setTitle(call.argument("title"))
                                    .setMessage(call.argument("message"))
                                    .setCancelable(false)
                                    .setPositiveButton(call.argument("positiveButtonText"), new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            result.success(true);
                                        }
                                    });
                            builder.create().show();
                        }

                        if ("dialog.confirm".equals(call.method)) {
                            AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this)
                                    .setTitle(call.argument("title"))
                                    .setMessage(call.argument("message"))
                                    .setCancelable(false)
                                    .setPositiveButton(call.argument("positiveButtonText"), new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            result.success(true);
                                        }
                                    })
                                    .setNegativeButton(call.argument("negativeButtonText"), new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            result.success(false);
                                        }
                                    });
                            builder.create().show();
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 123 && onboardingResult != null) {
            onboardingResult.success(true);
            onboardingResult = null;
        }
    }
}
