<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/onboarding_background_3"
    android:orientation="vertical"
    android:paddingTop="30dp"
    android:paddingBottom="60dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="36dp"
        android:paddingEnd="36dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="@string/lovesync"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/skip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:letterSpacing="0.13"
            android:paddingLeft="24dp"
            android:paddingTop="16dp"
            android:paddingRight="24dp"
            android:paddingBottom="16dp"
            android:text="@string/finish"
            android:textColor="@android:color/black"
            android:textSize="12sp"
            android:textStyle="bold" />

    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingLeft="20dp"
        android:paddingTop="20dp"
        android:paddingRight="75dp"
        android:paddingBottom="36dp">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/onboarding_3" />

    </FrameLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginEnd="76dp"
        android:fontFamily="sans-serif-condensed"
        android:includeFontPadding="false"
        android:letterSpacing="-0.01"
        android:text="@string/onboarding_title_3"
        android:textColor="@android:color/black"
        android:textSize="42sp"
        android:textStyle="bold" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="76dp"
        android:fontFamily="sans-serif-light"
        android:letterSpacing="-0.01"
        android:lineSpacingExtra="4sp"
        android:text="@string/onboarding_description_3"
        android:textColor="@android:color/black"
        android:textSize="16sp" />

</LinearLayout>